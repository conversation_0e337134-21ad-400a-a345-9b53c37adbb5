<script setup lang="ts">
const colorMode = useColorMode()

// 设置颜色模式
const setMode = (mode: 'light' | 'dark' | 'system') => {
  colorMode.preference = mode
}

// 检查是否为当前模式
const isCurrentMode = (mode: string) => {
  return colorMode.preference === mode
}
</script>

<template>
  <ClientOnly>
  <DropdownMenu>
    <DropdownMenuTrigger as-child>
      <Button variant="outline" size="icon">
        <Icon
          name="lucide:sun"
          class="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"
        />
        <Icon
          name="lucide:moon"
          class="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"
        />
        <span class="sr-only">Toggle theme</span>
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent align="end">
      <DropdownMenuItem @click="setMode('light')">
        <Icon name="lucide:sun" class="mr-2 h-4 w-4" />
        <span>Light</span>
        <Icon
          v-if="isCurrentMode('light')"
          name="lucide:check"
          class="ml-auto h-4 w-4"
        />
      </DropdownMenuItem>
      <DropdownMenuItem @click="setMode('dark')">
        <Icon name="lucide:moon" class="mr-2 h-4 w-4" />
        <span>Dark</span>
        <Icon
          v-if="isCurrentMode('dark')"
          name="lucide:check"
          class="ml-auto h-4 w-4"
        />
      </DropdownMenuItem>
      <DropdownMenuItem @click="setMode('system')">
        <Icon name="lucide:monitor" class="mr-2 h-4 w-4" />
        <span>System</span>
        <Icon
          v-if="isCurrentMode('system')"
          name="lucide:check"
          class="ml-auto h-4 w-4"
        />
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
  </ClientOnly>
</template>
