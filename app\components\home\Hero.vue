<script setup lang="ts">
</script>

<template>
  <div class="mx-auto max-w-4xl text-center">
    <div class="flex w-full items-center justify-center md:inline-flex">
      <a
        class="rainbowBorder mb-10 inline-flex h-[32px] items-center justify-center rounded-xl p-[2px] text-sm dark:p-px"
        href="/blog/pyrenees"
      >
        <span class="inline-flex h-full items-center gap-1 whitespace-nowrap rounded-xl bg-background px-3 py-2 dark:bg-background">New product for 2025</span>
      </a>
    </div>
    <h1 class="font-gradient md:text-76xl inline-block text-center text-6xl leading-[1.2] tracking-tight">
      Browser Bookmark Manager<br>
      Pro Extension
    </h1>
    <p class="mb-8 mt-4 text-center text-base leading-7 text-muted-foreground md:text-lg md:leading-7">
      Multiple folders, Tags & filters, Multiple copies, Batch editing, Ranking... and more <br>
      Works seamlessly with existing browser bookmarks
    </p>
    <div class="flex flex-col justify-center gap-4 md:flex-row">
      <Button
        size="lg"
        class="h-12 rounded-full text-base"
        as-child
      >
        <NuxtLink href="https://chromewebstore.google.com/detail/hkhpfcdoekegjilpmfmemgllocnpnbbd?utm_source=item-share-cb">
          Chrome Extension
        </NuxtLink>
      </Button>
      <Button
        size="lg"
        class="h-12 rounded-full text-base"
        variant="secondary"
        disabled
        as-child
      >
        <NuxtLink href="/">
          Edge Extension
        </NuxtLink>
      </Button>
    </div>
  </div>
</template>

<style scoped>
.rainbowBorder {
  background: linear-gradient(90deg, #02fcef70, #ffb52b70 50%, #a02bfe70);
  transition: transform .2s ease-in-out;
  transform-origin: 0 100%;
}
</style>
