import{d as g,c as r,o as e,a as t,t as i,u as k,b as a,F as $,r as N,e as f,f as c,g as S,h as y,i as V,j as v,_ as P,k as R}from"./framework._wHCgU2P.js";const T={class:"text-base leading-6 font-medium text-gray-500 dark:text-gray-300"},j=["datetime"],w=g({__name:"Date",props:{date:{}},setup(x){const s=x;function d(){return new Date(s.date.time).toISOString()}return(l,o)=>(e(),r("dl",null,[o[0]||(o[0]=t("dt",{class:"sr-only"},"Published on",-1)),t("dd",T,[t("time",{datetime:d()},i(l.date.string),9,j)])]))}}),h=JSON.parse('[{"title":"如何在博客中使用图片","url":"/blog/posts/how-to-use-images.html","excerpt":"","date":{"time":1754568000000,"string":"August 7, 2025"}},{"title":"2022 Year In Review","url":"/blog/posts/2022-year-in-review.html","excerpt":"<p>Happy new year, Vue community! With 2023 upon us, we would like to take this opportunity to recap what happened in 2022, and discuss what to expect in 2023.</p>\\n","date":{"time":1672574400000,"string":"January 1, 2023"}},{"title":"Reflections for 2020-2021","url":"/blog/posts/hello-2021.html","excerpt":"<p>With a new year upon us, we also have a new blog! In this post, we would like to take a look back at some of the highlights from 2020, as well as some ideas we have for 2021.</p>\\n","date":{"time":1610366400000,"string":"January 11, 2021"}}]'),A={class:"divide-y divide-gray-200 dark:divide-slate-200/5"},C={class:"pt-6 pb-8 space-y-2 md:space-y-5"},F={class:"text-3xl leading-9 font-extrabold text-gray-900 dark:text-white tracking-tight sm:text-4xl sm:leading-10 md:text-6xl md:leading-14"},I={class:"text-lg leading-7 text-gray-500 dark:text-white"},B={class:"divide-y divide-gray-200 dark:divide-slate-200/5"},D={class:"py-12"},H={class:"space-y-2 xl:grid xl:grid-cols-4 xl:space-y-0 xl:items-baseline"},L={class:"space-y-5 xl:col-span-3"},J={class:"space-y-6"},M={class:"text-2xl leading-8 font-bold tracking-tight"},O=["href"],W=["innerHTML"],E={class:"text-base leading-6 font-medium"},G=["href"],Y=g({__name:"Home",setup(x){const{frontmatter:s}=k();return(d,l)=>(e(),r("div",A,[t("div",C,[t("h1",F,i(a(s).title),1),t("p",I,i(a(s).subtext),1)]),t("ul",B,[(e(!0),r($,null,N(a(h),({title:o,url:p,date:m,excerpt:_})=>(e(),r("li",D,[t("article",H,[f(w,{date:m},null,8,["date"]),t("div",L,[t("div",J,[t("h2",M,[t("a",{class:"text-gray-900 dark:text-white",href:p},i(o),9,O)]),_?(e(),r("div",{key:0,class:"prose dark:prose-invert max-w-none text-gray-500 dark:text-gray-300",innerHTML:_},null,8,W)):c("",!0)]),t("div",E,[t("a",{class:"link","aria-label":"read more",href:p},"Read more →",8,G)])])])]))),256))])]))}}),q={class:"pt-6 pb-10 xl:pt-11 xl:border-b xl:border-gray-200 dark:xl:border-slate-200/5"},z={class:"flex justify-center xl:block space-x-8 sm:space-x-12 xl:space-x-0 xl:space-y-8"},K={class:"flex items-center space-x-2"},Q=["src"],U=["src"],X={class:"text-sm font-medium leading-5 whitespace-nowrap"},Z={class:"text-gray-900 dark:text-white"},tt={key:0,class:"sr-only"},et={key:1},st=["href"],at=g({__name:"Author",setup(x){const{frontmatter:s}=k();return(d,l)=>(e(),r("dl",q,[l[1]||(l[1]=t("dt",{class:"sr-only"},"Authors",-1)),t("dd",null,[t("ul",z,[t("li",K,[a(s).gravatar?(e(),r("img",{key:0,src:"https://gravatar.com/avatar/"+a(s).gravatar,alt:"author image",class:"w-10 h-10 rounded-full"},null,8,Q)):a(s).avatar?(e(),r("img",{key:1,src:a(s).avatar,alt:"author image",class:"w-10 h-10 rounded-full"},null,8,U)):c("",!0),t("dl",X,[l[0]||(l[0]=t("dt",{class:"sr-only"},"Name",-1)),t("dd",Z,i(a(s).author),1),a(s).twitter?(e(),r("dt",tt,"Twitter")):c("",!0),a(s).twitter?(e(),r("dd",et,[t("a",{href:"https://twitter.com/"+a(s).twitter,target:"_blank",rel:"noopnener noreferrer",class:"link"},i(a(s).twitter),9,st)])):c("",!0)])])])])]))}}),rt={class:"xl:divide-y xl:divide-gray-200 dark:xl:divide-slate-200/5"},ot={class:"pt-6 xl:pb-10 space-y-1 text-center"},nt={class:"text-3xl leading-9 font-extrabold text-gray-900 dark:text-white tracking-tight sm:text-4xl sm:leading-10 md:text-5xl md:leading-14"},lt={class:"divide-y xl:divide-y-0 divide-gray-200 dark:divide-slate-200/5 xl:grid xl:grid-cols-4 xl:gap-x-10 pb-16 xl:pb-20",style:{"grid-template-rows":"auto 1fr"}},it={class:"divide-y divide-gray-200 dark:divide-slate-200/5 xl:pb-0 xl:col-span-3 xl:row-span-2"},dt={class:"text-sm font-medium leading-5 divide-y divide-gray-200 dark:divide-slate-200/5 xl:col-start-1 xl:row-start-2"},ct={key:0,class:"py-8"},xt={class:"link"},_t=["href"],ut={key:1,class:"py-8"},ht={class:"link"},pt=["href"],mt=g({__name:"Article",setup(x){const{frontmatter:s}=k(),d=S();function l(){return h.findIndex(n=>n.url===d.path)}const o=y(()=>l()),p=y(()=>{const n=o.value;return n>=0?h[n].date:null}),m=y(()=>{const n=o.value;return n>0?h[n-1]:null}),_=y(()=>{const n=o.value;return n>=0&&n<h.length-1?h[n+1]:null});return(n,u)=>{const b=V("Content");return e(),r("article",rt,[t("header",ot,[p.value?(e(),v(w,{key:0,date:p.value},null,8,["date"])):c("",!0),t("h1",nt,i(a(s).title),1)]),t("div",lt,[f(at),t("div",it,[f(b,{class:"prose dark:prose-invert max-w-none pt-10 pb-8"})]),t("footer",dt,[m.value?(e(),r("div",ct,[u[0]||(u[0]=t("h2",{class:"text-xs tracking-wide uppercase text-gray-500 dark:text-white"}," Next Article ",-1)),t("div",xt,[t("a",{href:m.value.url},i(m.value.title),9,_t)])])):c("",!0),_.value?(e(),r("div",ut,[u[1]||(u[1]=t("h2",{class:"text-xs tracking-wide uppercase text-gray-500 dark:text-white"}," Previous Article ",-1)),t("div",ht,[t("a",{href:_.value.url},i(_.value.title),9,pt)])])):c("",!0),u[2]||(u[2]=t("div",{class:"pt-8"},[t("a",{class:"link",href:"/blog/"},"← Back to the blog")],-1))])])])}}}),gt={},yt={class:"text-3xl font-bold"};function vt(x,s){return e(),r("h1",yt,"404 Page Not Found")}const kt=P(gt,[["render",vt]]),ft={class:"antialiased dark:bg-slate-900"},wt={class:"max-w-3xl mx-auto px-4 sm:px-6 xl:max-w-5xl xl:px-0"},bt={class:"flex justify-between items-center py-10 font-bold"},$t={class:"text-xl",href:"/blog/","aria-label":"The Vue Point"},Nt={key:0,class:"hidden md:inline dark:text-white"},St={class:"max-w-3xl mx-auto px-4 sm:px-6 xl:max-w-5xl xl:px-0"},Vt=g({__name:"Layout",setup(x){const{page:s,frontmatter:d}=k();return(l,o)=>(e(),r("div",ft,[t("div",wt,[t("nav",bt,[t("a",$t,[o[0]||(o[0]=t("img",{class:"inline-block mr-2",style:{width:"36px",height:"31px"},alt:"logo",src:"/logo.svg"},null,-1)),a(d).index?c("",!0):(e(),r("span",Nt,"The Vue Point"))]),o[1]||(o[1]=R('<div class="text-sm text-gray-500 dark:text-white leading-5"><a class="hover:text-gray-700 dark:hover:text-gray-200" href="https://github.com/vuejs/blog" target="_blank" rel="noopener"><span class="hidden sm:inline">GitHub </span>Source</a><span class="mr-2 ml-2">·</span><a class="hover:text-gray-700 dark:hover:text-gray-200 vp-raw" href="/feed.rss">RSS<span class="hidden sm:inline"> Feed</span></a><span class="mr-2 ml-2">·</span><a class="hover:text-gray-700 dark:hover:text-gray-200" href="https://vuejs.org" target="_blank" rel="noopener">Vuejs.org →</a></div>',1))])]),t("main",St,[a(d).index?(e(),v(Y,{key:0})):a(s).isNotFound?(e(),v(kt,{key:1})):(e(),v(mt,{key:2}))])]))}}),Rt={Layout:Vt};export{Rt as R};
