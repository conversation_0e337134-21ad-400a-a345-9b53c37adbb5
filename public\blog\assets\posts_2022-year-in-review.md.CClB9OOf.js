import{_ as t,c as a,o as r,k as o}from"./chunks/framework._wHCgU2P.js";const m=JSON.parse('{"title":"2022 Year In Review","description":"","frontmatter":{"title":"2022 Year In Review","date":"2023-01-01T00:00:00.000Z","author":"Evan You","gravatar":"eca93da2c67aadafe35d477aa8f454b8","twitter":"@youyuxi"},"headers":[],"relativePath":"posts/2022-year-in-review.md","filePath":"posts/2022-year-in-review.md"}'),n={name:"posts/2022-year-in-review.md"};function i(s,e,l,p,h,u){return r(),a("div",null,e[0]||(e[0]=[o('<p>Happy new year, Vue community! With 2023 upon us, we would like to take this opportunity to recap what happened in 2022, and discuss what to expect in 2023.</p><hr><h2 id="recap-for-2022" tabindex="-1">Recap for 2022 <a class="header-anchor" href="#recap-for-2022" aria-label="Permalink to &quot;Recap for 2022&quot;">​</a></h2><p>In February 2022, we <a href="./hello-2021.html">switched Vue&#39;s default version to 3.x</a>. The switch marked the readiness of all the official parts of the framework for v3, including a major revamp of the documentation that provides guidance on latest best practices.</p><p>We are still in a transition period for the ecosystem to move to Vue 3. So after the switch, we focused more on improving Vue&#39;s developer experience by investing in tooling. Our team members have been actively involved in the development of <a href="https://vitejs.dev" target="_blank" rel="noreferrer">Vite</a>, and we made significant improvement to Vue&#39;s IDE and TypeScript support by shipping <a href="./hello-2021.html">Volar 1.0</a>.</p><p>Over the course of 2022, we saw the NPM usage of Vue 3 grew by <strong>almost 200%</strong>. On the community side, the Vue 3 ecosystem is now ripe with great solutions to help boost your productivity. Both <a href="https://nuxt.com" target="_blank" rel="noreferrer">Nuxt 3</a> and <a href="https://vuetifyjs.com" target="_blank" rel="noreferrer">Vuetify 3</a> reached stable status in November 2022, and <a href="https://github.com/nativescript-vue/nativescript-vue" target="_blank" rel="noreferrer">NativeScript for Vue 3</a> recently launched beta. In addition, we want to give a shout out to other great projects that had already supported Vue 3 for quite some time: <a href="https://quasar.dev/" target="_blank" rel="noreferrer">Quasar</a>, <a href="https://www.naiveui.com/" target="_blank" rel="noreferrer">NaiveUI</a>, <a href="https://ionicframework.com/docs/vue/overview" target="_blank" rel="noreferrer">Ionic Vue</a>, <a href="https://www.primefaces.org/primevue/" target="_blank" rel="noreferrer">PrimeVue</a>, <a href="https://www.inkline.io/" target="_blank" rel="noreferrer">InkLine</a>, <a href="https://element-plus.org/" target="_blank" rel="noreferrer">ElementPlus</a>, and <a href="https://twitter.com/vuejs/status/1599706412086878208" target="_blank" rel="noreferrer">more</a>.</p><p>Despite Vue 3 being now the default, we understand that many users have to stay on Vue 2 due to the cost of migration. To ensure that Vue 2 users benefit from the advancement of the framework, we decided to move Vue 2&#39;s source code to TypeScript and back-ported some of the most important Vue 3 features in <a href="./hello-2021.html">Vue 2.7</a>. We also made sure that Vite, Vue Devtools and Volar all simultaneously support Vue 2 and Vue 3.</p><h2 id="what-to-expect-in-2023" tabindex="-1">What to Expect in 2023 <a class="header-anchor" href="#what-to-expect-in-2023" aria-label="Permalink to &quot;What to Expect in 2023&quot;">​</a></h2><h3 id="smaller-and-more-frequent-minor-releases" tabindex="-1">Smaller and More Frequent Minor Releases <a class="header-anchor" href="#smaller-and-more-frequent-minor-releases" aria-label="Permalink to &quot;Smaller and More Frequent Minor Releases&quot;">​</a></h3><p>With the last Vue 2 minor release (2.7) out of the door, we expect to be full steam ahead shipping features for Vue 3 core in 2023. We have quite a long list of features that we are excited to work on!</p><p>One thing we would like to improve is our release cadence. Vue follows <a href="https://semver.org/" target="_blank" rel="noreferrer">semver</a>, which means we should only ship features in minor versions. In the past, we did a &quot;big minor&quot; approach where we group many features together in big, infrequent minor releases. This has resulted in quite some low-complexity features being blocked while we worked on other high-complexity ones. In 2023, we want to do smaller and more frequent minor releases so that we can get more features out, faster.</p><p>This also means we will be adjusting what goes into 3.3. Originally, we planned to graduate Suspense and Reactivity Transform from experimental status in 3.3. However, we feel that both still need further RFC discussion, and they should not block other more straightforward features to land. Now, the goal of 3.3 is to land proposed / planned features that are clear wins and do not require RFC discussion - for example, supporting externally imported types in <code>&lt;script setup&gt;</code> macros.</p><p>In parallel to that, we will:</p><ol><li>Further evaluate the readiness of Suspense and Reactivity Transform.</li><li>Spend time to evaluate outstanding user-submitted RFCs and feature requests.</li><li>Post RFCs for features that we intend to land in 3.4 and beyond, for example SSR lazy hydration.</li></ol><p>Expect more details later this month.</p><p>Another thing to note is there is no plan for big breaking changes for the foreseeable future. Acknowledging the challenges users faced during the v2 to v3 transition, we want to have a better long term upgrade story for Vue going forward.</p><h3 id="vapor-mode" tabindex="-1">Vapor Mode <a class="header-anchor" href="#vapor-mode" aria-label="Permalink to &quot;Vapor Mode&quot;">​</a></h3><p>Vapor Mode is an alternative compilation strategy that we have been experimenting with, inspired by <a href="https://www.solidjs.com/" target="_blank" rel="noreferrer">Solid</a>. Given the same Vue SFC, Vapor Mode compiles it into JavaScript output that is more performant, uses less memory, and requires less runtime support code compared to the current Virtual DOM based output. It is still in early phase, but here are some high level points:</p><ul><li><p>Vapor Mode is intended for use cases where performance is the primary concern. It is opt-in and does not affect existing codebases.</p></li><li><p>At the very least, you will be able to embed a Vapor component subtree into any existing Vue 3 app. Ideally, we hope to achieve granular opt-in at the component level, which means freely mixing Vapor and non-Vapor components in the same app.</p></li><li><p>Building an app with only Vapor components allows you to drop the Virtual DOM runtime from the bundle, significantly reducing the baseline runtime size.</p></li><li><p>In order to achieve the best performance, Vapor Mode will only support a subset of Vue features. In particular, Vapor Mode components will only support Composition API and <code>&lt;script setup&gt;</code>. However, this supported subset will work exactly the same between Vapor and non-Vapor components.</p></li></ul><p>We will share more details as we make more progress later in the year.</p><h3 id="conferences" tabindex="-1">Conferences <a class="header-anchor" href="#conferences" aria-label="Permalink to &quot;Conferences&quot;">​</a></h3><p>There are already many in-person Vue conferences lined up for 2023:</p><ul><li><a href="https://vuejs.amsterdam/" target="_blank" rel="noreferrer">Vue.js Amsterdam</a> - Feb 9-10, Amsterdam, The Netherlands</li><li><a href="https://vuejslive.com/" target="_blank" rel="noreferrer">Vue.js Live</a> - May 12 &amp; 15th, London, UK</li><li><a href="https://us.vuejs.org/" target="_blank" rel="noreferrer">VueConf US</a> - May 24-26th, New Orleans, USA</li><li>VueFes Japan - October 28th, Tokyo, Japan (info TBA)</li></ul><p>I (Evan) plan to attend all of these in person. After almost 3 years of absence, I can&#39;t wait to meet the community again - please come say hi!</p><h3 id="one-year-until-vue-2-eol" tabindex="-1">One Year Until Vue 2 EOL <a class="header-anchor" href="#one-year-until-vue-2-eol" aria-label="Permalink to &quot;One Year Until Vue 2 EOL&quot;">​</a></h3><p>As a reminder, today marks <strong>exactly one year until the end of Vue 2 support</strong>. We have created a page explaining the implication of this and outlining the options for those who expect to be using Vue 2 beyond the EOL date: <a href="https://v2.vuejs.org/lts/" target="_blank" rel="noreferrer">Details on Vue 2 EOL and Extended Support</a>.</p>',26)]))}const c=t(n,[["render",i]]);export{m as __pageData,c as default};
