import { acceptHMRUpdate, defineStore } from 'pinia'

interface Config {
  radius: number
}

export const useThemeStore = defineStore('theme-config', () => {
  const config = reactive<Config>({
    radius: 0.5,
  })

  const radius = computed(() => config.radius)

  function setRadius(newRadius: number) {
    config.radius = newRadius
    if (import.meta.client) {
      document.documentElement.style.setProperty('--radius', `${newRadius}rem`)
      const iframes = document.querySelectorAll('iframe')
      // set iframe radius
      iframes.forEach((iframe) => {
        try {
          const iframeDocument = iframe.contentDocument || iframe.contentWindow!.document
          if (iframeDocument)
            iframeDocument.documentElement.style.setProperty('--radius', `${newRadius}rem`)
        }
        catch (error) {
          console.error(`iframe error: ${error}`)
        }
      })
    }
  }

  return {
    config,
    radius,
    setRadius,
  }
}, {
  persist: true,
})

if (import.meta.hot)
  import.meta.hot.accept(acceptHMRUpdate(useThemeStore, import.meta.hot))
