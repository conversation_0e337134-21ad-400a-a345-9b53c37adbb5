<template>
  <div class="prose prose-slate max-w-none dark:prose-invert">
    <h1>License</h1>

    <p>Read about the licensing terms for Acme. Have a question? Feel free to <a href="mailto:<EMAIL>">contact us</a>.</p>

    <p>Acme grants you an ongoing, non-exclusive license to <strong>use</strong> the download files. The license grants permission to one individual (the Licensee) to access and use the download files.</p>

    <h2>Permissible Actions:</h2>

    <ul>
      <li>Use the download files to create unlimited End Products.</li>
      <li>Use the download files to create unlimited End Products for unlimited Clients.</li>
      <li>Use the download files to create End Products where the End Product is sold to End Users.</li>
      <li>Use the download files to create End Products that are open source and freely available to End Users following review and approval by Acme.</li>
      <li>Sell the End Products. The License will survive the sale of the End Product.</li>
    </ul>

    <h2>Prohibited Actions:</h2>

    <ul>
      <li>Use the download files to create End Products that are designed to allow an End User to build their own End Products using the download files or derivatives of the download files.</li>
      <li>Re-distribute the download files or derivatives of the download files separately from an End Product, neither in code or as design assets.</li>
      <li>Share your access to the download files with any other individuals</li>
    </ul>

    <h2>Permissible Examples</h2>

    <p>Examples of usage allowed by the license:</p>

    <ul>
      <li>Creating a personal website by yourself</li>
      <li>Creating a website or web application for a client that will be owned by that client</li>
      <li>Creating a commercial SaaS application (like an invoicing app for example) where end users have to pay a fee to use the application</li>
      <li>Creating a commercial self-hosted web application that is sold to end users for a one-time fee</li>
      <li>Creating a web application where the primary purpose is clearly not to simply re-distribute the download files (like a conference organization app that uses the download files for its UI for example) that is free and open source, where the source code is publicly available</li>
    </ul>

    <h2>Examples of usage not allowed by the license:</h2>

    <ul>
      <li>Creating a theme, template, or project starter kit using the download files and making it available either for sale or for free</li>
    </ul>

    <p>In simple terms, use Acme for anything you like except for projects that compete with Acme and would have a negative impact on our product sales.</p>

    <h2>Developer License Definitions</h2>

    <ul>
      <li>Licensee is the individual who has purchased a Standard License.</li>
      <li>Download files are the files that are downloaded from the Acme repository.</li>
      <li>End Product is any artifact produced that incorporates the download files or derivatives of the download files.</li>
      <li>End User is a user of an End Product.</li>
      <li>Client is an individual or entity receiving custom professional services directly from the Licensee, produced specifically for that individual or entity. Customers of software-as-a-service products are not considered clients for the purpose of this document.</li>
    </ul>

    <h2>Team License</h2>

    <p>Acme grants you an ongoing, non-exclusive license to use the download files. The license grants permission to one individual (the Licensee) to access and use the download files.</p>

    <p>The license grants permission to all Employees and Contractors of the Licensee to access and use the download files.</p>

    <h2>Permissible Actions:</h2>

    <ul>
      <li>Use the download files to create unlimited End Products.</li>
      <li>Use the download files to create unlimited End Products for unlimited Clients.</li>
      <li>Use the download files to create End Products where the End Product is sold to End Users.</li>
      <li>Use the download files to create End Products that are open source and freely available to End Users following review and approval by Acme.</li>
      <li>Sell the End Products. The License will survive the sale of the End Product.</li>
    </ul>

    <h2>Prohibited Actions:</h2>

    <ul>
      <li>Use the download files to create End Products that are designed to allow an End User to build their own End Products using the download files or derivatives of the download files.</li>
      <li>Re-distribute the download files or derivatives of the download files separately from an End Product.</li>
    </ul>

    <h2>Example usage</h2>

    <h3>Examples of usage allowed by the license:</h3>

    <ul>
      <li>Creating a website for your company</li>
      <li>Creating a website or web application for a client that will be owned by that client</li>
      <li>Creating a commercial SaaS application (like an invoicing app for example) where end users have to pay a fee to use the application</li>
      <li>Creating a commercial self-hosted web application that is sold to end users for a one-time fee</li>
      <li>Creating a web application where the primary purpose is clearly not to simply re-distribute the download files (like a conference organization app that uses the download files for its UI for example) that is free and open source, where the source code is publicly available</li>
    </ul>

    <h3>Examples of use not allowed by the license:</h3>

    <ul>
      <li>Creating a theme or template using the download files and making it available either for sale for for free</li>
      <li>Creating any End Product that is not the sole property of either your company or a client of your company. For example your employees/contractors can not use your company Acme license to build their own websites or side projects.</li>
    </ul>

    <h2>Team License Definitions</h2>

    <ul>
      <li>Licensee is the business entity who has purchased a Team License.</li>
      <li>Download files are the files that are downloaded from the Acme repository.</li>
      <li>End Product is any artifact produced that incorporates the download files.</li>
      <li>End User is a user of an End Product.</li>
      <li>Employee is a full-time or part-time employee of the Licensee.</li>
      <li>Contractor is an individual or business entity contracted to perform services for the Licensee.</li>
      <li>Client is an individual or entity receiving custom professional services directly from the Licensee, produced specifically for that individual or entity. Customers of software-as-a-service products are not considered clients for the purpose of this document.</li>
    </ul>

    <h2>Liability</h2>

    <p>Acme's liability to you for costs, damages, or other losses arising from your use of the download files — including third-party claims against you — is limited to a refund of your license fee. Acme may not be held liable for any consequential damages related to your use of the download files.</p>

    <h2>Refunds</h2>

    <p>Due to the non-returnable nature of this product, we do not offer refunds.</p>
  </div>
</template>

<script setup lang="ts">
// 设置页面元数据
useSeoMeta({
  title: 'License',
  description: 'Get started with Nuxt UI Pro documentation template.',
})
</script>