<!DOCTYPE html>
<html lang="en-US" dir="ltr">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title>Runtime API Examples | Docs</title>
    <meta name="description" content="A VitePress Site">
    <meta name="generator" content="VitePress v1.6.4">
    <link rel="preload stylesheet" href="/docs/assets/style.Pr8Em1Yw.css" as="style">
    <link rel="preload stylesheet" href="/docs/vp-icons.css" as="style">
    
    <script type="module" src="/docs/assets/app.D-0YxH2M.js"></script>
    <link rel="preload" href="/docs/assets/inter-roman-latin.Di8DUHzh.woff2" as="font" type="font/woff2" crossorigin="">
    <link rel="modulepreload" href="/docs/assets/chunks/theme.C2qKzy7Z.js">
    <link rel="modulepreload" href="/docs/assets/chunks/framework.B-Fvu48Z.js">
    <link rel="modulepreload" href="/docs/assets/api-examples.md.DVIbrpp8.lean.js">
    <script id="check-dark-mode">(()=>{const e=localStorage.getItem("vitepress-theme-appearance")||"auto",a=window.matchMedia("(prefers-color-scheme: dark)").matches;(!e||e==="auto"?a:e==="dark")&&document.documentElement.classList.add("dark")})();</script>
    <script id="check-mac-os">document.documentElement.classList.toggle("mac",/Mac|iPhone|iPod|iPad/i.test(navigator.platform));</script>
  </head>
  <body>
    <div id="app"><div class="Layout" data-v-642b817b><!--[--><!--]--><!--[--><span tabindex="-1" data-v-1de352b9></span><a href="#VPContent" class="VPSkipLink visually-hidden" data-v-1de352b9>Skip to content</a><!--]--><!----><header class="VPNav" data-v-642b817b data-v-e8e79344><div class="VPNavBar" data-v-e8e79344 data-v-a9bc4c2c><div class="wrapper" data-v-a9bc4c2c><div class="container" data-v-a9bc4c2c><div class="title" data-v-a9bc4c2c><div class="VPNavBarTitle has-sidebar" data-v-a9bc4c2c data-v-aba688c7><a class="title" href="/docs/" data-v-aba688c7><!--[--><!--]--><!----><span data-v-aba688c7>Docs</span><!--[--><!--]--></a></div></div><div class="content" data-v-a9bc4c2c><div class="content-body" data-v-a9bc4c2c><!--[--><!--]--><div class="VPNavBarSearch search" data-v-a9bc4c2c><!----></div><nav aria-labelledby="main-nav-aria-label" class="VPNavBarMenu menu" data-v-a9bc4c2c data-v-19d0c076><span id="main-nav-aria-label" class="visually-hidden" data-v-19d0c076> Main Navigation </span><!--[--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/docs/" tabindex="0" data-v-19d0c076 data-v-dc1cc5dd><!--[--><span data-v-dc1cc5dd>Home</span><!--]--></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/docs/markdown-examples.html" tabindex="0" data-v-19d0c076 data-v-dc1cc5dd><!--[--><span data-v-dc1cc5dd>Examples</span><!--]--></a><!--]--><!--]--></nav><!----><div class="VPNavBarAppearance appearance" data-v-a9bc4c2c data-v-013018db><button class="VPSwitch VPSwitchAppearance" type="button" role="switch" title aria-checked="false" data-v-013018db data-v-198d9ae7 data-v-dd2b1ac1><span class="check" data-v-dd2b1ac1><span class="icon" data-v-dd2b1ac1><!--[--><span class="vpi-sun sun" data-v-198d9ae7></span><span class="vpi-moon moon" data-v-198d9ae7></span><!--]--></span></span></button></div><div class="VPSocialLinks VPNavBarSocialLinks social-links" data-v-a9bc4c2c data-v-c28d7ee2 data-v-026eecd3><!--[--><a class="VPSocialLink no-icon" href="https://github.com/vuejs/vitepress" aria-label="github" target="_blank" rel="noopener" data-v-026eecd3 data-v-13066e67><span class="vpi-social-github"></span></a><!--]--></div><div class="VPFlyout VPNavBarExtra extra" data-v-a9bc4c2c data-v-19608f77 data-v-f320e8c7><button type="button" class="button" aria-haspopup="true" aria-expanded="false" aria-label="extra navigation" data-v-f320e8c7><span class="vpi-more-horizontal icon" data-v-f320e8c7></span></button><div class="menu" data-v-f320e8c7><div class="VPMenu" data-v-f320e8c7 data-v-33d8f694><!----><!--[--><!--[--><!----><div class="group" data-v-19608f77><div class="item appearance" data-v-19608f77><p class="label" data-v-19608f77>Appearance</p><div class="appearance-action" data-v-19608f77><button class="VPSwitch VPSwitchAppearance" type="button" role="switch" title aria-checked="false" data-v-19608f77 data-v-198d9ae7 data-v-dd2b1ac1><span class="check" data-v-dd2b1ac1><span class="icon" data-v-dd2b1ac1><!--[--><span class="vpi-sun sun" data-v-198d9ae7></span><span class="vpi-moon moon" data-v-198d9ae7></span><!--]--></span></span></button></div></div></div><div class="group" data-v-19608f77><div class="item social-links" data-v-19608f77><div class="VPSocialLinks social-links-list" data-v-19608f77 data-v-026eecd3><!--[--><a class="VPSocialLink no-icon" href="https://github.com/vuejs/vitepress" aria-label="github" target="_blank" rel="noopener" data-v-026eecd3 data-v-13066e67><span class="vpi-social-github"></span></a><!--]--></div></div></div><!--]--><!--]--></div></div></div><!--[--><!--]--><button type="button" class="VPNavBarHamburger hamburger" aria-label="mobile navigation" aria-expanded="false" aria-controls="VPNavScreen" data-v-a9bc4c2c data-v-77657797><span class="container" data-v-77657797><span class="top" data-v-77657797></span><span class="middle" data-v-77657797></span><span class="bottom" data-v-77657797></span></span></button></div></div></div></div><div class="divider" data-v-a9bc4c2c><div class="divider-line" data-v-a9bc4c2c></div></div></div><!----></header><div class="VPLocalNav has-sidebar empty" data-v-642b817b data-v-66045814><div class="container" data-v-66045814><button class="menu" aria-expanded="false" aria-controls="VPSidebarNav" data-v-66045814><span class="vpi-align-left menu-icon" data-v-66045814></span><span class="menu-text" data-v-66045814>Menu</span></button><div class="VPLocalNavOutlineDropdown" style="--vp-vh:0px;" data-v-66045814 data-v-1e951d9a><button data-v-1e951d9a>Return to top</button><!----></div></div></div><aside class="VPSidebar" data-v-642b817b data-v-5d4efa1a><div class="curtain" data-v-5d4efa1a></div><nav class="nav" id="VPSidebarNav" aria-labelledby="sidebar-aria-label" tabindex="-1" data-v-5d4efa1a><span class="visually-hidden" id="sidebar-aria-label" data-v-5d4efa1a> Sidebar Navigation </span><!--[--><!--]--><!--[--><div class="no-transition group" data-v-3cb554eb><section class="VPSidebarItem level-0 has-active" data-v-3cb554eb data-v-3fc49afd><div class="item" role="button" tabindex="0" data-v-3fc49afd><div class="indicator" data-v-3fc49afd></div><h2 class="text" data-v-3fc49afd>Examples</h2><!----></div><div class="items" data-v-3fc49afd><!--[--><div class="VPSidebarItem level-1 is-link" data-v-3fc49afd data-v-3fc49afd><div class="item" data-v-3fc49afd><div class="indicator" data-v-3fc49afd></div><a class="VPLink link link" href="/docs/markdown-examples.html" data-v-3fc49afd><!--[--><p class="text" data-v-3fc49afd>Markdown Examples</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-3fc49afd data-v-3fc49afd><div class="item" data-v-3fc49afd><div class="indicator" data-v-3fc49afd></div><a class="VPLink link link" href="/docs/api-examples.html" data-v-3fc49afd><!--[--><p class="text" data-v-3fc49afd>Runtime API Examples</p><!--]--></a><!----></div><!----></div><!--]--></div></section></div><!--]--><!--[--><!--]--></nav></aside><div class="VPContent has-sidebar" id="VPContent" data-v-642b817b data-v-cd9cd26e><div class="VPDoc has-sidebar has-aside" data-v-cd9cd26e data-v-2f279d96><!--[--><!--]--><div class="container" data-v-2f279d96><div class="aside" data-v-2f279d96><div class="aside-curtain" data-v-2f279d96></div><div class="aside-container" data-v-2f279d96><div class="aside-content" data-v-2f279d96><div class="VPDocAside" data-v-2f279d96 data-v-6fe2c2dd><!--[--><!--]--><!--[--><!--]--><nav aria-labelledby="doc-outline-aria-label" class="VPDocAsideOutline" data-v-6fe2c2dd data-v-2c116cb8><div class="content" data-v-2c116cb8><div class="outline-marker" data-v-2c116cb8></div><div aria-level="2" class="outline-title" id="doc-outline-aria-label" role="heading" data-v-2c116cb8>On this page</div><ul class="VPDocOutlineItem root" data-v-2c116cb8 data-v-424258f6><!--[--><!--]--></ul></div></nav><!--[--><!--]--><div class="spacer" data-v-6fe2c2dd></div><!--[--><!--]--><!----><!--[--><!--]--><!--[--><!--]--></div></div></div></div><div class="content" data-v-2f279d96><div class="content-container" data-v-2f279d96><!--[--><!--]--><main class="main" data-v-2f279d96><div style="position:relative;" class="vp-doc _docs_api-examples" data-v-2f279d96><div><h1 id="runtime-api-examples" tabindex="-1">Runtime API Examples <a class="header-anchor" href="#runtime-api-examples" aria-label="Permalink to &quot;Runtime API Examples&quot;">​</a></h1><p>This page demonstrates usage of some of the runtime APIs provided by VitePress.</p><p>The main <code>useData()</code> API can be used to access site, theme, and page data for the current page. It works in both <code>.md</code> and <code>.vue</code> files:</p><div class="language-md vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">md</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;script setup&gt;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">import { useData } from &#39;vitepress&#39;</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">const { theme, page, frontmatter } = useData()</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;/script&gt;</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-light-font-weight:bold;--shiki-dark:#79B8FF;--shiki-dark-font-weight:bold;">## Results</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-light-font-weight:bold;--shiki-dark:#79B8FF;--shiki-dark-font-weight:bold;">### Theme Data</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;pre&gt;{{ theme }}&lt;/pre&gt;</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-light-font-weight:bold;--shiki-dark:#79B8FF;--shiki-dark-font-weight:bold;">### Page Data</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;pre&gt;{{ page }}&lt;/pre&gt;</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-light-font-weight:bold;--shiki-dark:#79B8FF;--shiki-dark-font-weight:bold;">### Page Frontmatter</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;pre&gt;{{ frontmatter }}&lt;/pre&gt;</span></span></code></pre></div><h2 id="results" tabindex="-1">Results <a class="header-anchor" href="#results" aria-label="Permalink to &quot;Results&quot;">​</a></h2><h3 id="theme-data" tabindex="-1">Theme Data <a class="header-anchor" href="#theme-data" aria-label="Permalink to &quot;Theme Data&quot;">​</a></h3><pre>{
  &quot;nav&quot;: [
    {
      &quot;text&quot;: &quot;Home&quot;,
      &quot;link&quot;: &quot;/&quot;
    },
    {
      &quot;text&quot;: &quot;Examples&quot;,
      &quot;link&quot;: &quot;/markdown-examples&quot;
    }
  ],
  &quot;sidebar&quot;: [
    {
      &quot;text&quot;: &quot;Examples&quot;,
      &quot;items&quot;: [
        {
          &quot;text&quot;: &quot;Markdown Examples&quot;,
          &quot;link&quot;: &quot;/markdown-examples&quot;
        },
        {
          &quot;text&quot;: &quot;Runtime API Examples&quot;,
          &quot;link&quot;: &quot;/api-examples&quot;
        }
      ]
    }
  ],
  &quot;socialLinks&quot;: [
    {
      &quot;icon&quot;: &quot;github&quot;,
      &quot;link&quot;: &quot;https://github.com/vuejs/vitepress&quot;
    }
  ]
}</pre><h3 id="page-data" tabindex="-1">Page Data <a class="header-anchor" href="#page-data" aria-label="Permalink to &quot;Page Data&quot;">​</a></h3><pre>{
  &quot;title&quot;: &quot;Runtime API Examples&quot;,
  &quot;description&quot;: &quot;&quot;,
  &quot;frontmatter&quot;: {
    &quot;outline&quot;: &quot;deep&quot;
  },
  &quot;headers&quot;: [],
  &quot;relativePath&quot;: &quot;api-examples.md&quot;,
  &quot;filePath&quot;: &quot;api-examples.md&quot;
}</pre><h3 id="page-frontmatter" tabindex="-1">Page Frontmatter <a class="header-anchor" href="#page-frontmatter" aria-label="Permalink to &quot;Page Frontmatter&quot;">​</a></h3><pre>{
  &quot;outline&quot;: &quot;deep&quot;
}</pre><h2 id="more" tabindex="-1">More <a class="header-anchor" href="#more" aria-label="Permalink to &quot;More&quot;">​</a></h2><p>Check out the documentation for the <a href="https://vitepress.dev/reference/runtime-api#usedata" target="_blank" rel="noreferrer">full list of runtime APIs</a>.</p></div></div></main><footer class="VPDocFooter" data-v-2f279d96 data-v-1fb9e5ba><!--[--><!--]--><!----><nav class="prev-next" aria-labelledby="doc-footer-aria-label" data-v-1fb9e5ba><span class="visually-hidden" id="doc-footer-aria-label" data-v-1fb9e5ba>Pager</span><div class="pager" data-v-1fb9e5ba><a class="VPLink link pager-link prev" href="/docs/markdown-examples.html" data-v-1fb9e5ba><!--[--><span class="desc" data-v-1fb9e5ba>Previous page</span><span class="title" data-v-1fb9e5ba>Markdown Examples</span><!--]--></a></div><div class="pager" data-v-1fb9e5ba><!----></div></nav></footer><!--[--><!--]--></div></div></div><!--[--><!--]--></div></div><!----><!--[--><!--]--></div></div>
    <script>window.__VP_HASH_MAP__=JSON.parse("{\"api-examples.md\":\"DVIbrpp8\",\"index.md\":\"BAc_RmYd\",\"markdown-examples.md\":\"DHvLEJTg\"}");window.__VP_SITE_DATA__=JSON.parse("{\"lang\":\"en-US\",\"dir\":\"ltr\",\"title\":\"Docs\",\"description\":\"A VitePress Site\",\"base\":\"/docs/\",\"head\":[],\"router\":{\"prefetchLinks\":true},\"appearance\":true,\"themeConfig\":{\"nav\":[{\"text\":\"Home\",\"link\":\"/\"},{\"text\":\"Examples\",\"link\":\"/markdown-examples\"}],\"sidebar\":[{\"text\":\"Examples\",\"items\":[{\"text\":\"Markdown Examples\",\"link\":\"/markdown-examples\"},{\"text\":\"Runtime API Examples\",\"link\":\"/api-examples\"}]}],\"socialLinks\":[{\"icon\":\"github\",\"link\":\"https://github.com/vuejs/vitepress\"}]},\"locales\":{},\"scrollOffset\":134,\"cleanUrls\":false}");</script>
    
  </body>
</html>