<script setup lang="ts">
const colorMode = useColorMode()

// 使用 ref 来管理 Switch 的状态
const isDark = ref(colorMode.value === 'dark')

// 监听 colorMode 的变化，同步到 Switch
watch(() => colorMode.value, (newValue) => {
  isDark.value = newValue === 'dark'
})

// 监听 Switch 的变化，同步到 colorMode
watch(isDark, (newValue) => {
  colorMode.preference = newValue ? 'dark' : 'light'
})
</script>

<template>
  <div class="flex items-center gap-2">
    <Icon
      name="lucide:sun"
      class="h-4 w-4 text-muted-foreground transition-colors"
      :class="{ 'text-yellow-500': !isDark }"
    />
    <Switch
      v-model:checked="isDark"
      aria-label="Toggle dark mode"
    />
    <Icon
      name="lucide:moon"
      class="h-4 w-4 text-muted-foreground transition-colors"
      :class="{ 'text-blue-400': isDark }"
    />
  </div>
</template>
