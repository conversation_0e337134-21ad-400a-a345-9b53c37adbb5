/**
* @vue/shared v3.5.17
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Ms(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const ne={},At=[],ke=()=>{},Ao=()=>!1,Qt=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Is=e=>e.startsWith("onUpdate:"),ue=Object.assign,Ps=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Ro=Object.prototype.hasOwnProperty,Q=(e,t)=>Ro.call(e,t),B=Array.isArray,Rt=e=>On(e)==="[object Map]",qr=e=>On(e)==="[object Set]",G=e=>typeof e=="function",le=e=>typeof e=="string",Je=e=>typeof e=="symbol",se=e=>e!==null&&typeof e=="object",Gr=e=>(se(e)||G(e))&&G(e.then)&&G(e.catch),Xr=Object.prototype.toString,On=e=>Xr.call(e),Oo=e=>On(e).slice(8,-1),Yr=e=>On(e)==="[object Object]",Ls=e=>le(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Ot=Ms(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Mn=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Mo=/-(\w)/g,Ne=Mn(e=>e.replace(Mo,(t,n)=>n?n.toUpperCase():"")),Io=/\B([A-Z])/g,lt=Mn(e=>e.replace(Io,"-$1").toLowerCase()),In=Mn(e=>e.charAt(0).toUpperCase()+e.slice(1)),mn=Mn(e=>e?`on${In(e)}`:""),rt=(e,t)=>!Object.is(e,t),Gn=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},ms=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},Po=e=>{const t=parseFloat(e);return isNaN(t)?e:t},Lo=e=>{const t=le(e)?Number(e):NaN;return isNaN(t)?e:t};let tr;const Pn=()=>tr||(tr=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Ns(e){if(B(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=le(s)?Do(s):Ns(s);if(r)for(const i in r)t[i]=r[i]}return t}else if(le(e)||se(e))return e}const No=/;(?![^(]*\))/g,Fo=/:([^]+)/,Ho=/\/\*[^]*?\*\//g;function Do(e){const t={};return e.replace(Ho,"").split(No).forEach(n=>{if(n){const s=n.split(Fo);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function Fs(e){let t="";if(le(e))t=e;else if(B(e))for(let n=0;n<e.length;n++){const s=Fs(e[n]);s&&(t+=s+" ")}else if(se(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const $o="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",jo=Ms($o);function zr(e){return!!e||e===""}const Jr=e=>!!(e&&e.__v_isRef===!0),Vo=e=>le(e)?e:e==null?"":B(e)||se(e)&&(e.toString===Xr||!G(e.toString))?Jr(e)?Vo(e.value):JSON.stringify(e,Qr,2):String(e),Qr=(e,t)=>Jr(t)?Qr(e,t.value):Rt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,r],i)=>(n[Xn(s,i)+" =>"]=r,n),{})}:qr(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Xn(n))}:Je(t)?Xn(t):se(t)&&!B(t)&&!Yr(t)?String(t):t,Xn=(e,t="")=>{var n;return Je(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ve;class Wo{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=ve,!t&&ve&&(this.index=(ve.scopes||(ve.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=ve;try{return ve=this,t()}finally{ve=n}}}on(){++this._on===1&&(this.prevScope=ve,ve=this)}off(){this._on>0&&--this._on===0&&(ve=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function Zr(){return ve}function ko(e,t=!1){ve&&ve.cleanups.push(e)}let te;const Yn=new WeakSet;class ei{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,ve&&ve.active&&ve.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Yn.has(this)&&(Yn.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||ni(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,nr(this),si(this);const t=te,n=He;te=this,He=!0;try{return this.fn()}finally{ri(this),te=t,He=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)$s(t);this.deps=this.depsTail=void 0,nr(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Yn.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){vs(this)&&this.run()}get dirty(){return vs(this)}}let ti=0,jt,Vt;function ni(e,t=!1){if(e.flags|=8,t){e.next=Vt,Vt=e;return}e.next=jt,jt=e}function Hs(){ti++}function Ds(){if(--ti>0)return;if(Vt){let t=Vt;for(Vt=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;jt;){let t=jt;for(jt=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function si(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function ri(e){let t,n=e.depsTail,s=n;for(;s;){const r=s.prevDep;s.version===-1?(s===n&&(n=r),$s(s),Uo(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=r}e.deps=t,e.depsTail=n}function vs(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(ii(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function ii(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Bt)||(e.globalVersion=Bt,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!vs(e))))return;e.flags|=2;const t=e.dep,n=te,s=He;te=e,He=!0;try{si(e);const r=e.fn(e._value);(t.version===0||rt(r,e._value))&&(e.flags|=128,e._value=r,t.version++)}catch(r){throw t.version++,r}finally{te=n,He=s,ri(e),e.flags&=-3}}function $s(e,t=!1){const{dep:n,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let i=n.computed.deps;i;i=i.nextDep)$s(i,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Uo(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let He=!0;const oi=[];function Ge(){oi.push(He),He=!1}function Xe(){const e=oi.pop();He=e===void 0?!0:e}function nr(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=te;te=void 0;try{t()}finally{te=n}}}let Bt=0;class Bo{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Ln{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!te||!He||te===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==te)n=this.activeLink=new Bo(te,this),te.deps?(n.prevDep=te.depsTail,te.depsTail.nextDep=n,te.depsTail=n):te.deps=te.depsTail=n,li(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=te.depsTail,n.nextDep=void 0,te.depsTail.nextDep=n,te.depsTail=n,te.deps===n&&(te.deps=s)}return n}trigger(t){this.version++,Bt++,this.notify(t)}notify(t){Hs();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Ds()}}}function li(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)li(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const wn=new WeakMap,pt=Symbol(""),ys=Symbol(""),Kt=Symbol("");function _e(e,t,n){if(He&&te){let s=wn.get(e);s||wn.set(e,s=new Map);let r=s.get(n);r||(s.set(n,r=new Ln),r.map=s,r.key=n),r.track()}}function qe(e,t,n,s,r,i){const o=wn.get(e);if(!o){Bt++;return}const l=c=>{c&&c.trigger()};if(Hs(),t==="clear")o.forEach(l);else{const c=B(e),u=c&&Ls(n);if(c&&n==="length"){const a=Number(s);o.forEach((h,v)=>{(v==="length"||v===Kt||!Je(v)&&v>=a)&&l(h)})}else switch((n!==void 0||o.has(void 0))&&l(o.get(n)),u&&l(o.get(Kt)),t){case"add":c?u&&l(o.get("length")):(l(o.get(pt)),Rt(e)&&l(o.get(ys)));break;case"delete":c||(l(o.get(pt)),Rt(e)&&l(o.get(ys)));break;case"set":Rt(e)&&l(o.get(pt));break}}Ds()}function Ko(e,t){const n=wn.get(e);return n&&n.get(t)}function Tt(e){const t=J(e);return t===e?t:(_e(t,"iterate",Kt),Ie(e)?t:t.map(de))}function Nn(e){return _e(e=J(e),"iterate",Kt),e}const qo={__proto__:null,[Symbol.iterator](){return zn(this,Symbol.iterator,de)},concat(...e){return Tt(this).concat(...e.map(t=>B(t)?Tt(t):t))},entries(){return zn(this,"entries",e=>(e[1]=de(e[1]),e))},every(e,t){return Ue(this,"every",e,t,void 0,arguments)},filter(e,t){return Ue(this,"filter",e,t,n=>n.map(de),arguments)},find(e,t){return Ue(this,"find",e,t,de,arguments)},findIndex(e,t){return Ue(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Ue(this,"findLast",e,t,de,arguments)},findLastIndex(e,t){return Ue(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Ue(this,"forEach",e,t,void 0,arguments)},includes(...e){return Jn(this,"includes",e)},indexOf(...e){return Jn(this,"indexOf",e)},join(e){return Tt(this).join(e)},lastIndexOf(...e){return Jn(this,"lastIndexOf",e)},map(e,t){return Ue(this,"map",e,t,void 0,arguments)},pop(){return Ht(this,"pop")},push(...e){return Ht(this,"push",e)},reduce(e,...t){return sr(this,"reduce",e,t)},reduceRight(e,...t){return sr(this,"reduceRight",e,t)},shift(){return Ht(this,"shift")},some(e,t){return Ue(this,"some",e,t,void 0,arguments)},splice(...e){return Ht(this,"splice",e)},toReversed(){return Tt(this).toReversed()},toSorted(e){return Tt(this).toSorted(e)},toSpliced(...e){return Tt(this).toSpliced(...e)},unshift(...e){return Ht(this,"unshift",e)},values(){return zn(this,"values",de)}};function zn(e,t,n){const s=Nn(e),r=s[t]();return s!==e&&!Ie(e)&&(r._next=r.next,r.next=()=>{const i=r._next();return i.value&&(i.value=n(i.value)),i}),r}const Go=Array.prototype;function Ue(e,t,n,s,r,i){const o=Nn(e),l=o!==e&&!Ie(e),c=o[t];if(c!==Go[t]){const h=c.apply(e,i);return l?de(h):h}let u=n;o!==e&&(l?u=function(h,v){return n.call(this,de(h),v,e)}:n.length>2&&(u=function(h,v){return n.call(this,h,v,e)}));const a=c.call(o,u,s);return l&&r?r(a):a}function sr(e,t,n,s){const r=Nn(e);let i=n;return r!==e&&(Ie(e)?n.length>3&&(i=function(o,l,c){return n.call(this,o,l,c,e)}):i=function(o,l,c){return n.call(this,o,de(l),c,e)}),r[t](i,...s)}function Jn(e,t,n){const s=J(e);_e(s,"iterate",Kt);const r=s[t](...n);return(r===-1||r===!1)&&Ws(n[0])?(n[0]=J(n[0]),s[t](...n)):r}function Ht(e,t,n=[]){Ge(),Hs();const s=J(e)[t].apply(e,n);return Ds(),Xe(),s}const Xo=Ms("__proto__,__v_isRef,__isVue"),ci=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Je));function Yo(e){Je(e)||(e=String(e));const t=J(this);return _e(t,"has",e),t.hasOwnProperty(e)}class ai{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const r=this._isReadonly,i=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return i;if(n==="__v_raw")return s===(r?i?il:hi:i?di:ui).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const o=B(t);if(!r){let c;if(o&&(c=qo[n]))return c;if(n==="hasOwnProperty")return Yo}const l=Reflect.get(t,n,ae(t)?t:s);return(Je(n)?ci.has(n):Xo(n))||(r||_e(t,"get",n),i)?l:ae(l)?o&&Ls(n)?l:l.value:se(l)?r?Fn(l):Pt(l):l}}class fi extends ai{constructor(t=!1){super(!1,t)}set(t,n,s,r){let i=t[n];if(!this._isShallow){const c=it(i);if(!Ie(s)&&!it(s)&&(i=J(i),s=J(s)),!B(t)&&ae(i)&&!ae(s))return c?!1:(i.value=s,!0)}const o=B(t)&&Ls(n)?Number(n)<t.length:Q(t,n),l=Reflect.set(t,n,s,ae(t)?t:r);return t===J(r)&&(o?rt(s,i)&&qe(t,"set",n,s):qe(t,"add",n,s)),l}deleteProperty(t,n){const s=Q(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&s&&qe(t,"delete",n,void 0),r}has(t,n){const s=Reflect.has(t,n);return(!Je(n)||!ci.has(n))&&_e(t,"has",n),s}ownKeys(t){return _e(t,"iterate",B(t)?"length":pt),Reflect.ownKeys(t)}}class zo extends ai{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Jo=new fi,Qo=new zo,Zo=new fi(!0);const _s=e=>e,rn=e=>Reflect.getPrototypeOf(e);function el(e,t,n){return function(...s){const r=this.__v_raw,i=J(r),o=Rt(i),l=e==="entries"||e===Symbol.iterator&&o,c=e==="keys"&&o,u=r[e](...s),a=n?_s:t?Sn:de;return!t&&_e(i,"iterate",c?ys:pt),{next(){const{value:h,done:v}=u.next();return v?{value:h,done:v}:{value:l?[a(h[0]),a(h[1])]:a(h),done:v}},[Symbol.iterator](){return this}}}}function on(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function tl(e,t){const n={get(r){const i=this.__v_raw,o=J(i),l=J(r);e||(rt(r,l)&&_e(o,"get",r),_e(o,"get",l));const{has:c}=rn(o),u=t?_s:e?Sn:de;if(c.call(o,r))return u(i.get(r));if(c.call(o,l))return u(i.get(l));i!==o&&i.get(r)},get size(){const r=this.__v_raw;return!e&&_e(J(r),"iterate",pt),Reflect.get(r,"size",r)},has(r){const i=this.__v_raw,o=J(i),l=J(r);return e||(rt(r,l)&&_e(o,"has",r),_e(o,"has",l)),r===l?i.has(r):i.has(r)||i.has(l)},forEach(r,i){const o=this,l=o.__v_raw,c=J(l),u=t?_s:e?Sn:de;return!e&&_e(c,"iterate",pt),l.forEach((a,h)=>r.call(i,u(a),u(h),o))}};return ue(n,e?{add:on("add"),set:on("set"),delete:on("delete"),clear:on("clear")}:{add(r){!t&&!Ie(r)&&!it(r)&&(r=J(r));const i=J(this);return rn(i).has.call(i,r)||(i.add(r),qe(i,"add",r,r)),this},set(r,i){!t&&!Ie(i)&&!it(i)&&(i=J(i));const o=J(this),{has:l,get:c}=rn(o);let u=l.call(o,r);u||(r=J(r),u=l.call(o,r));const a=c.call(o,r);return o.set(r,i),u?rt(i,a)&&qe(o,"set",r,i):qe(o,"add",r,i),this},delete(r){const i=J(this),{has:o,get:l}=rn(i);let c=o.call(i,r);c||(r=J(r),c=o.call(i,r)),l&&l.call(i,r);const u=i.delete(r);return c&&qe(i,"delete",r,void 0),u},clear(){const r=J(this),i=r.size!==0,o=r.clear();return i&&qe(r,"clear",void 0,void 0),o}}),["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=el(r,e,t)}),n}function js(e,t){const n=tl(e,t);return(s,r,i)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?s:Reflect.get(Q(n,r)&&r in s?n:s,r,i)}const nl={get:js(!1,!1)},sl={get:js(!1,!0)},rl={get:js(!0,!1)};const ui=new WeakMap,di=new WeakMap,hi=new WeakMap,il=new WeakMap;function ol(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function ll(e){return e.__v_skip||!Object.isExtensible(e)?0:ol(Oo(e))}function Pt(e){return it(e)?e:Vs(e,!1,Jo,nl,ui)}function cl(e){return Vs(e,!1,Zo,sl,di)}function Fn(e){return Vs(e,!0,Qo,rl,hi)}function Vs(e,t,n,s,r){if(!se(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=ll(e);if(i===0)return e;const o=r.get(e);if(o)return o;const l=new Proxy(e,i===2?s:n);return r.set(e,l),l}function gt(e){return it(e)?gt(e.__v_raw):!!(e&&e.__v_isReactive)}function it(e){return!!(e&&e.__v_isReadonly)}function Ie(e){return!!(e&&e.__v_isShallow)}function Ws(e){return e?!!e.__v_raw:!1}function J(e){const t=e&&e.__v_raw;return t?J(t):e}function vn(e){return!Q(e,"__v_skip")&&Object.isExtensible(e)&&ms(e,"__v_skip",!0),e}const de=e=>se(e)?Pt(e):e,Sn=e=>se(e)?Fn(e):e;function ae(e){return e?e.__v_isRef===!0:!1}function mt(e){return pi(e,!1)}function Pe(e){return pi(e,!0)}function pi(e,t){return ae(e)?e:new al(e,t)}class al{constructor(t,n){this.dep=new Ln,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:J(t),this._value=n?t:de(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||Ie(t)||it(t);t=s?t:J(t),rt(t,n)&&(this._rawValue=t,this._value=s?t:de(t),this.dep.trigger())}}function ks(e){return ae(e)?e.value:e}function ce(e){return G(e)?e():ks(e)}const fl={get:(e,t,n)=>t==="__v_raw"?e:ks(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return ae(r)&&!ae(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function gi(e){return gt(e)?e:new Proxy(e,fl)}class ul{constructor(t){this.__v_isRef=!0,this._value=void 0;const n=this.dep=new Ln,{get:s,set:r}=t(n.track.bind(n),n.trigger.bind(n));this._get=s,this._set=r}get value(){return this._value=this._get()}set value(t){this._set(t)}}function dl(e){return new ul(e)}class hl{constructor(t,n,s){this._object=t,this._key=n,this._defaultValue=s,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Ko(J(this._object),this._key)}}class pl{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function gl(e,t,n){return ae(e)?e:G(e)?new pl(e):se(e)&&arguments.length>1?ml(e,t,n):mt(e)}function ml(e,t,n){const s=e[t];return ae(s)?s:new hl(e,t,n)}class vl{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Ln(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Bt-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&te!==this)return ni(this,!0),!0}get value(){const t=this.dep.track();return ii(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function yl(e,t,n=!1){let s,r;return G(e)?s=e:(s=e.get,r=e.set),new vl(s,r,n)}const ln={},xn=new WeakMap;let dt;function _l(e,t=!1,n=dt){if(n){let s=xn.get(n);s||xn.set(n,s=[]),s.push(e)}}function bl(e,t,n=ne){const{immediate:s,deep:r,once:i,scheduler:o,augmentJob:l,call:c}=n,u=g=>r?g:Ie(g)||r===!1||r===0?st(g,1):st(g);let a,h,v,y,A=!1,P=!1;if(ae(e)?(h=()=>e.value,A=Ie(e)):gt(e)?(h=()=>u(e),A=!0):B(e)?(P=!0,A=e.some(g=>gt(g)||Ie(g)),h=()=>e.map(g=>{if(ae(g))return g.value;if(gt(g))return u(g);if(G(g))return c?c(g,2):g()})):G(e)?t?h=c?()=>c(e,2):e:h=()=>{if(v){Ge();try{v()}finally{Xe()}}const g=dt;dt=a;try{return c?c(e,3,[y]):e(y)}finally{dt=g}}:h=ke,t&&r){const g=h,M=r===!0?1/0:r;h=()=>st(g(),M)}const K=Zr(),H=()=>{a.stop(),K&&K.active&&Ps(K.effects,a)};if(i&&t){const g=t;t=(...M)=>{g(...M),H()}}let k=P?new Array(e.length).fill(ln):ln;const p=g=>{if(!(!(a.flags&1)||!a.dirty&&!g))if(t){const M=a.run();if(r||A||(P?M.some((W,R)=>rt(W,k[R])):rt(M,k))){v&&v();const W=dt;dt=a;try{const R=[M,k===ln?void 0:P&&k[0]===ln?[]:k,y];k=M,c?c(t,3,R):t(...R)}finally{dt=W}}}else a.run()};return l&&l(p),a=new ei(h),a.scheduler=o?()=>o(p,!1):p,y=g=>_l(g,!1,a),v=a.onStop=()=>{const g=xn.get(a);if(g){if(c)c(g,4);else for(const M of g)M();xn.delete(a)}},t?s?p(!0):k=a.run():o?o(p.bind(null,!0),!0):a.run(),H.pause=a.pause.bind(a),H.resume=a.resume.bind(a),H.stop=H,H}function st(e,t=1/0,n){if(t<=0||!se(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,ae(e))st(e.value,t,n);else if(B(e))for(let s=0;s<e.length;s++)st(e[s],t,n);else if(qr(e)||Rt(e))e.forEach(s=>{st(s,t,n)});else if(Yr(e)){for(const s in e)st(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&st(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Zt(e,t,n,s){try{return s?e(...s):e()}catch(r){Hn(r,t,n)}}function De(e,t,n,s){if(G(e)){const r=Zt(e,t,n,s);return r&&Gr(r)&&r.catch(i=>{Hn(i,t,n)}),r}if(B(e)){const r=[];for(let i=0;i<e.length;i++)r.push(De(e[i],t,n,s));return r}}function Hn(e,t,n,s=!0){const r=t?t.vnode:null,{errorHandler:i,throwUnhandledErrorInProduction:o}=t&&t.appContext.config||ne;if(t){let l=t.parent;const c=t.proxy,u=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const a=l.ec;if(a){for(let h=0;h<a.length;h++)if(a[h](e,c,u)===!1)return}l=l.parent}if(i){Ge(),Zt(i,null,10,[e,c,u]),Xe();return}}wl(e,n,r,s,o)}function wl(e,t,n,s=!0,r=!1){if(r)throw e;console.error(e)}const we=[];let Ve=-1;const Mt=[];let et=null,Ct=0;const mi=Promise.resolve();let Tn=null;function Dn(e){const t=Tn||mi;return e?t.then(this?e.bind(this):e):t}function Sl(e){let t=Ve+1,n=we.length;for(;t<n;){const s=t+n>>>1,r=we[s],i=qt(r);i<e||i===e&&r.flags&2?t=s+1:n=s}return t}function Us(e){if(!(e.flags&1)){const t=qt(e),n=we[we.length-1];!n||!(e.flags&2)&&t>=qt(n)?we.push(e):we.splice(Sl(t),0,e),e.flags|=1,vi()}}function vi(){Tn||(Tn=mi.then(yi))}function xl(e){B(e)?Mt.push(...e):et&&e.id===-1?et.splice(Ct+1,0,e):e.flags&1||(Mt.push(e),e.flags|=1),vi()}function rr(e,t,n=Ve+1){for(;n<we.length;n++){const s=we[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;we.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function En(e){if(Mt.length){const t=[...new Set(Mt)].sort((n,s)=>qt(n)-qt(s));if(Mt.length=0,et){et.push(...t);return}for(et=t,Ct=0;Ct<et.length;Ct++){const n=et[Ct];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}et=null,Ct=0}}const qt=e=>e.id==null?e.flags&2?-1:1/0:e.id;function yi(e){try{for(Ve=0;Ve<we.length;Ve++){const t=we[Ve];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Zt(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Ve<we.length;Ve++){const t=we[Ve];t&&(t.flags&=-2)}Ve=-1,we.length=0,En(),Tn=null,(we.length||Mt.length)&&yi()}}let pe=null,_i=null;function Cn(e){const t=pe;return pe=e,_i=e&&e.type.__scopeId||null,t}function Tl(e,t=pe,n){if(!t||e._n)return e;const s=(...r)=>{s._d&&mr(-1);const i=Cn(t);let o;try{o=e(...r)}finally{Cn(i),s._d&&mr(1)}return o};return s._n=!0,s._c=!0,s._d=!0,s}function We(e,t,n,s){const r=e.dirs,i=t&&t.dirs;for(let o=0;o<r.length;o++){const l=r[o];i&&(l.oldValue=i[o].value);let c=l.dir[s];c&&(Ge(),De(c,n,8,[e.el,l,e,t]),Xe())}}const El=Symbol("_vte"),bi=e=>e.__isTeleport,tt=Symbol("_leaveCb"),cn=Symbol("_enterCb");function Cl(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Lt(()=>{e.isMounted=!0}),Oi(()=>{e.isUnmounting=!0}),e}const Re=[Function,Array],wi={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Re,onEnter:Re,onAfterEnter:Re,onEnterCancelled:Re,onBeforeLeave:Re,onLeave:Re,onAfterLeave:Re,onLeaveCancelled:Re,onBeforeAppear:Re,onAppear:Re,onAfterAppear:Re,onAppearCancelled:Re},Si=e=>{const t=e.subTree;return t.component?Si(t.component):t},Al={name:"BaseTransition",props:wi,setup(e,{slots:t}){const n=en(),s=Cl();return()=>{const r=t.default&&Ei(t.default(),!0);if(!r||!r.length)return;const i=xi(r),o=J(e),{mode:l}=o;if(s.isLeaving)return Qn(i);const c=ir(i);if(!c)return Qn(i);let u=bs(c,o,s,n,h=>u=h);c.type!==he&&Gt(c,u);let a=n.subTree&&ir(n.subTree);if(a&&a.type!==he&&!ht(c,a)&&Si(n).type!==he){let h=bs(a,o,s,n);if(Gt(a,h),l==="out-in"&&c.type!==he)return s.isLeaving=!0,h.afterLeave=()=>{s.isLeaving=!1,n.job.flags&8||n.update(),delete h.afterLeave,a=void 0},Qn(i);l==="in-out"&&c.type!==he?h.delayLeave=(v,y,A)=>{const P=Ti(s,a);P[String(a.key)]=a,v[tt]=()=>{y(),v[tt]=void 0,delete u.delayedLeave,a=void 0},u.delayedLeave=()=>{A(),delete u.delayedLeave,a=void 0}}:a=void 0}else a&&(a=void 0);return i}}};function xi(e){let t=e[0];if(e.length>1){for(const n of e)if(n.type!==he){t=n;break}}return t}const Rl=Al;function Ti(e,t){const{leavingVNodes:n}=e;let s=n.get(t.type);return s||(s=Object.create(null),n.set(t.type,s)),s}function bs(e,t,n,s,r){const{appear:i,mode:o,persisted:l=!1,onBeforeEnter:c,onEnter:u,onAfterEnter:a,onEnterCancelled:h,onBeforeLeave:v,onLeave:y,onAfterLeave:A,onLeaveCancelled:P,onBeforeAppear:K,onAppear:H,onAfterAppear:k,onAppearCancelled:p}=t,g=String(e.key),M=Ti(n,e),W=(T,I)=>{T&&De(T,s,9,I)},R=(T,I)=>{const E=I[1];W(T,I),B(T)?T.every(_=>_.length<=1)&&E():T.length<=1&&E()},U={mode:o,persisted:l,beforeEnter(T){let I=c;if(!n.isMounted)if(i)I=K||c;else return;T[tt]&&T[tt](!0);const E=M[g];E&&ht(e,E)&&E.el[tt]&&E.el[tt](),W(I,[T])},enter(T){let I=u,E=a,_=h;if(!n.isMounted)if(i)I=H||u,E=k||a,_=p||h;else return;let N=!1;const Y=T[cn]=re=>{N||(N=!0,re?W(_,[T]):W(E,[T]),U.delayedLeave&&U.delayedLeave(),T[cn]=void 0)};I?R(I,[T,Y]):Y()},leave(T,I){const E=String(e.key);if(T[cn]&&T[cn](!0),n.isUnmounting)return I();W(v,[T]);let _=!1;const N=T[tt]=Y=>{_||(_=!0,I(),Y?W(P,[T]):W(A,[T]),T[tt]=void 0,M[E]===e&&delete M[E])};M[E]=e,y?R(y,[T,N]):N()},clone(T){const I=bs(T,t,n,s,r);return r&&r(I),I}};return U}function Qn(e){if($n(e))return e=ot(e),e.children=null,e}function ir(e){if(!$n(e))return bi(e.type)&&e.children?xi(e.children):e;if(e.component)return e.component.subTree;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&G(n.default))return n.default()}}function Gt(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Gt(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Ei(e,t=!1,n){let s=[],r=0;for(let i=0;i<e.length;i++){let o=e[i];const l=n==null?o.key:String(n)+String(o.key!=null?o.key:i);o.type===Se?(o.patchFlag&128&&r++,s=s.concat(Ei(o.children,t,l))):(t||o.type!==he)&&s.push(l!=null?ot(o,{key:l}):o)}if(r>1)for(let i=0;i<s.length;i++)s[i].patchFlag=-2;return s}/*! #__NO_SIDE_EFFECTS__ */function Ci(e,t){return G(e)?ue({name:e.name},t,{setup:e}):e}function Ai(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function It(e,t,n,s,r=!1){if(B(e)){e.forEach((A,P)=>It(A,t&&(B(t)?t[P]:t),n,s,r));return}if(vt(s)&&!r){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&It(e,t,n,s.component.subTree);return}const i=s.shapeFlag&4?Gs(s.component):s.el,o=r?null:i,{i:l,r:c}=e,u=t&&t.r,a=l.refs===ne?l.refs={}:l.refs,h=l.setupState,v=J(h),y=h===ne?()=>!1:A=>Q(v,A);if(u!=null&&u!==c&&(le(u)?(a[u]=null,y(u)&&(h[u]=null)):ae(u)&&(u.value=null)),G(c))Zt(c,l,12,[o,a]);else{const A=le(c),P=ae(c);if(A||P){const K=()=>{if(e.f){const H=A?y(c)?h[c]:a[c]:c.value;r?B(H)&&Ps(H,i):B(H)?H.includes(i)||H.push(i):A?(a[c]=[i],y(c)&&(h[c]=a[c])):(c.value=[i],e.k&&(a[e.k]=c.value))}else A?(a[c]=o,y(c)&&(h[c]=o)):P&&(c.value=o,e.k&&(a[e.k]=o))};o?(K.id=-1,Ce(K,n)):K()}}}let or=!1;const Et=()=>{or||(console.error("Hydration completed but contains mismatches."),or=!0)},Ol=e=>e.namespaceURI.includes("svg")&&e.tagName!=="foreignObject",Ml=e=>e.namespaceURI.includes("MathML"),an=e=>{if(e.nodeType===1){if(Ol(e))return"svg";if(Ml(e))return"mathml"}},fn=e=>e.nodeType===8;function Il(e){const{mt:t,p:n,o:{patchProp:s,createText:r,nextSibling:i,parentNode:o,remove:l,insert:c,createComment:u}}=e,a=(p,g)=>{if(!g.hasChildNodes()){n(null,p,g),En(),g._vnode=p;return}h(g.firstChild,p,null,null,null),En(),g._vnode=p},h=(p,g,M,W,R,U=!1)=>{U=U||!!g.dynamicChildren;const T=fn(p)&&p.data==="[",I=()=>P(p,g,M,W,R,T),{type:E,ref:_,shapeFlag:N,patchFlag:Y}=g;let re=p.nodeType;g.el=p,Y===-2&&(U=!1,g.dynamicChildren=null);let j=null;switch(E){case bt:re!==3?g.children===""?(c(g.el=r(""),o(p),p),j=p):j=I():(p.data!==g.children&&(Et(),p.data=g.children),j=i(p));break;case he:k(p)?(j=i(p),H(g.el=p.content.firstChild,p,M)):re!==8||T?j=I():j=i(p);break;case kt:if(T&&(p=i(p),re=p.nodeType),re===1||re===3){j=p;const X=!g.children.length;for(let D=0;D<g.staticCount;D++)X&&(g.children+=j.nodeType===1?j.outerHTML:j.data),D===g.staticCount-1&&(g.anchor=j),j=i(j);return T?i(j):j}else I();break;case Se:T?j=A(p,g,M,W,R,U):j=I();break;default:if(N&1)(re!==1||g.type.toLowerCase()!==p.tagName.toLowerCase())&&!k(p)?j=I():j=v(p,g,M,W,R,U);else if(N&6){g.slotScopeIds=R;const X=o(p);if(T?j=K(p):fn(p)&&p.data==="teleport start"?j=K(p,p.data,"teleport end"):j=i(p),t(g,X,null,M,W,an(X),U),vt(g)&&!g.type.__asyncResolved){let D;T?(D=ge(Se),D.anchor=j?j.previousSibling:X.lastChild):D=p.nodeType===3?ro(""):ge("div"),D.el=p,g.component.subTree=D}}else N&64?re!==8?j=I():j=g.type.hydrate(p,g,M,W,R,U,e,y):N&128&&(j=g.type.hydrate(p,g,M,W,an(o(p)),R,U,e,h))}return _!=null&&It(_,null,W,g),j},v=(p,g,M,W,R,U)=>{U=U||!!g.dynamicChildren;const{type:T,props:I,patchFlag:E,shapeFlag:_,dirs:N,transition:Y}=g,re=T==="input"||T==="option";if(re||E!==-1){N&&We(g,null,M,"created");let j=!1;if(k(p)){j=qi(null,Y)&&M&&M.vnode.props&&M.vnode.props.appear;const D=p.content.firstChild;if(j){const oe=D.getAttribute("class");oe&&(D.$cls=oe),Y.beforeEnter(D)}H(D,p,M),g.el=p=D}if(_&16&&!(I&&(I.innerHTML||I.textContent))){let D=y(p.firstChild,g,p,M,W,R,U);for(;D;){un(p,1)||Et();const oe=D;D=D.nextSibling,l(oe)}}else if(_&8){let D=g.children;D[0]===`
`&&(p.tagName==="PRE"||p.tagName==="TEXTAREA")&&(D=D.slice(1)),p.textContent!==D&&(un(p,0)||Et(),p.textContent=g.children)}if(I){if(re||!U||E&48){const D=p.tagName.includes("-");for(const oe in I)(re&&(oe.endsWith("value")||oe==="indeterminate")||Qt(oe)&&!Ot(oe)||oe[0]==="."||D)&&s(p,oe,null,I[oe],void 0,M)}else if(I.onClick)s(p,"onClick",null,I.onClick,void 0,M);else if(E&4&&gt(I.style))for(const D in I.style)I.style[D]}let X;(X=I&&I.onVnodeBeforeMount)&&Oe(X,M,g),N&&We(g,null,M,"beforeMount"),((X=I&&I.onVnodeMounted)||N||j)&&eo(()=>{X&&Oe(X,M,g),j&&Y.enter(p),N&&We(g,null,M,"mounted")},W)}return p.nextSibling},y=(p,g,M,W,R,U,T)=>{T=T||!!g.dynamicChildren;const I=g.children,E=I.length;for(let _=0;_<E;_++){const N=T?I[_]:I[_]=Me(I[_]),Y=N.type===bt;p?(Y&&!T&&_+1<E&&Me(I[_+1]).type===bt&&(c(r(p.data.slice(N.children.length)),M,i(p)),p.data=N.children),p=h(p,N,W,R,U,T)):Y&&!N.children?c(N.el=r(""),M):(un(M,1)||Et(),n(null,N,M,null,W,R,an(M),U))}return p},A=(p,g,M,W,R,U)=>{const{slotScopeIds:T}=g;T&&(R=R?R.concat(T):T);const I=o(p),E=y(i(p),g,I,M,W,R,U);return E&&fn(E)&&E.data==="]"?i(g.anchor=E):(Et(),c(g.anchor=u("]"),I,E),E)},P=(p,g,M,W,R,U)=>{if(un(p.parentElement,1)||Et(),g.el=null,U){const E=K(p);for(;;){const _=i(p);if(_&&_!==E)l(_);else break}}const T=i(p),I=o(p);return l(p),n(null,g,I,T,M,W,an(I),R),M&&(M.vnode.el=g.el,Qi(M,g.el)),T},K=(p,g="[",M="]")=>{let W=0;for(;p;)if(p=i(p),p&&fn(p)&&(p.data===g&&W++,p.data===M)){if(W===0)return i(p);W--}return p},H=(p,g,M)=>{const W=g.parentNode;W&&W.replaceChild(p,g);let R=M;for(;R;)R.vnode.el===g&&(R.vnode.el=R.subTree.el=p),R=R.parent},k=p=>p.nodeType===1&&p.tagName==="TEMPLATE";return[a,h]}const lr="data-allow-mismatch",Pl={0:"text",1:"children",2:"class",3:"style",4:"attribute"};function un(e,t){if(t===0||t===1)for(;e&&!e.hasAttribute(lr);)e=e.parentElement;const n=e&&e.getAttribute(lr);if(n==null)return!1;if(n==="")return!0;{const s=n.split(",");return t===0&&s.includes("children")?!0:s.includes(Pl[t])}}Pn().requestIdleCallback;Pn().cancelIdleCallback;const vt=e=>!!e.type.__asyncLoader,$n=e=>e.type.__isKeepAlive;function Ll(e,t){Ri(e,"a",t)}function Nl(e,t){Ri(e,"da",t)}function Ri(e,t,n=fe){const s=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(jn(t,s,n),n){let r=n.parent;for(;r&&r.parent;)$n(r.parent.vnode)&&Fl(s,t,n,r),r=r.parent}}function Fl(e,t,n,s){const r=jn(t,e,s,!0);Vn(()=>{Ps(s[t],r)},n)}function jn(e,t,n=fe,s=!1){if(n){const r=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...o)=>{Ge();const l=tn(n),c=De(t,n,e,o);return l(),Xe(),c});return s?r.unshift(i):r.push(i),i}}const Qe=e=>(t,n=fe)=>{(!zt||e==="sp")&&jn(e,(...s)=>t(...s),n)},Hl=Qe("bm"),Lt=Qe("m"),Dl=Qe("bu"),$l=Qe("u"),Oi=Qe("bum"),Vn=Qe("um"),jl=Qe("sp"),Vl=Qe("rtg"),Wl=Qe("rtc");function kl(e,t=fe){jn("ec",e,t)}const Mi="components";function ff(e,t){return Pi(Mi,e,!0,t)||e}const Ii=Symbol.for("v-ndc");function uf(e){return le(e)?Pi(Mi,e,!1)||e:e||Ii}function Pi(e,t,n=!0,s=!1){const r=pe||fe;if(r){const i=r.type;{const l=Cc(i,!1);if(l&&(l===t||l===Ne(t)||l===In(Ne(t))))return i}const o=cr(r[e]||i[e],t)||cr(r.appContext[e],t);return!o&&s?i:o}}function cr(e,t){return e&&(e[t]||e[Ne(t)]||e[In(Ne(t))])}function df(e,t,n,s){let r;const i=n,o=B(e);if(o||le(e)){const l=o&&gt(e);let c=!1,u=!1;l&&(c=!Ie(e),u=it(e),e=Nn(e)),r=new Array(e.length);for(let a=0,h=e.length;a<h;a++)r[a]=t(c?u?Sn(de(e[a])):de(e[a]):e[a],a,void 0,i)}else if(typeof e=="number"){r=new Array(e);for(let l=0;l<e;l++)r[l]=t(l+1,l,void 0,i)}else if(se(e))if(e[Symbol.iterator])r=Array.from(e,(l,c)=>t(l,c,void 0,i));else{const l=Object.keys(e);r=new Array(l.length);for(let c=0,u=l.length;c<u;c++){const a=l[c];r[c]=t(e[a],a,c,i)}}else r=[];return r}function hf(e,t,n={},s,r){if(pe.ce||pe.parent&&vt(pe.parent)&&pe.parent.ce)return t!=="default"&&(n.name=t),Es(),Cs(Se,null,[ge("slot",n,s&&s())],64);let i=e[t];i&&i._c&&(i._d=!1),Es();const o=i&&Li(i(n)),l=n.key||o&&o.key,c=Cs(Se,{key:(l&&!Je(l)?l:`_${t}`)+(!o&&s?"_fb":"")},o||(s?s():[]),o&&e._===1?64:-2);return!r&&c.scopeId&&(c.slotScopeIds=[c.scopeId+"-s"]),i&&i._c&&(i._d=!0),c}function Li(e){return e.some(t=>Yt(t)?!(t.type===he||t.type===Se&&!Li(t.children)):!0)?e:null}function pf(e,t){const n={};for(const s in e)n[/[A-Z]/.test(s)?`on:${s}`:mn(s)]=e[s];return n}const ws=e=>e?io(e)?Gs(e):ws(e.parent):null,Wt=ue(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>ws(e.parent),$root:e=>ws(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Fi(e),$forceUpdate:e=>e.f||(e.f=()=>{Us(e.update)}),$nextTick:e=>e.n||(e.n=Dn.bind(e.proxy)),$watch:e=>fc.bind(e)}),Zn=(e,t)=>e!==ne&&!e.__isScriptSetup&&Q(e,t),Ul={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:r,props:i,accessCache:o,type:l,appContext:c}=e;let u;if(t[0]!=="$"){const y=o[t];if(y!==void 0)switch(y){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return i[t]}else{if(Zn(s,t))return o[t]=1,s[t];if(r!==ne&&Q(r,t))return o[t]=2,r[t];if((u=e.propsOptions[0])&&Q(u,t))return o[t]=3,i[t];if(n!==ne&&Q(n,t))return o[t]=4,n[t];Ss&&(o[t]=0)}}const a=Wt[t];let h,v;if(a)return t==="$attrs"&&_e(e.attrs,"get",""),a(e);if((h=l.__cssModules)&&(h=h[t]))return h;if(n!==ne&&Q(n,t))return o[t]=4,n[t];if(v=c.config.globalProperties,Q(v,t))return v[t]},set({_:e},t,n){const{data:s,setupState:r,ctx:i}=e;return Zn(r,t)?(r[t]=n,!0):s!==ne&&Q(s,t)?(s[t]=n,!0):Q(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:i}},o){let l;return!!n[o]||e!==ne&&Q(e,o)||Zn(t,o)||(l=i[0])&&Q(l,o)||Q(s,o)||Q(Wt,o)||Q(r.config.globalProperties,o)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:Q(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function gf(){return Bl().slots}function Bl(){const e=en();return e.setupContext||(e.setupContext=lo(e))}function ar(e){return B(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Ss=!0;function Kl(e){const t=Fi(e),n=e.proxy,s=e.ctx;Ss=!1,t.beforeCreate&&fr(t.beforeCreate,e,"bc");const{data:r,computed:i,methods:o,watch:l,provide:c,inject:u,created:a,beforeMount:h,mounted:v,beforeUpdate:y,updated:A,activated:P,deactivated:K,beforeDestroy:H,beforeUnmount:k,destroyed:p,unmounted:g,render:M,renderTracked:W,renderTriggered:R,errorCaptured:U,serverPrefetch:T,expose:I,inheritAttrs:E,components:_,directives:N,filters:Y}=t;if(u&&ql(u,s,null),o)for(const X in o){const D=o[X];G(D)&&(s[X]=D.bind(n))}if(r){const X=r.call(n,n);se(X)&&(e.data=Pt(X))}if(Ss=!0,i)for(const X in i){const D=i[X],oe=G(D)?D.bind(n,n):G(D.get)?D.get.bind(n,n):ke,nn=!G(D)&&G(D.set)?D.set.bind(n):ke,ct=ie({get:oe,set:nn});Object.defineProperty(s,X,{enumerable:!0,configurable:!0,get:()=>ct.value,set:$e=>ct.value=$e})}if(l)for(const X in l)Ni(l[X],s,n,X);if(c){const X=G(c)?c.call(n):c;Reflect.ownKeys(X).forEach(D=>{Ql(D,X[D])})}a&&fr(a,e,"c");function j(X,D){B(D)?D.forEach(oe=>X(oe.bind(n))):D&&X(D.bind(n))}if(j(Hl,h),j(Lt,v),j(Dl,y),j($l,A),j(Ll,P),j(Nl,K),j(kl,U),j(Wl,W),j(Vl,R),j(Oi,k),j(Vn,g),j(jl,T),B(I))if(I.length){const X=e.exposed||(e.exposed={});I.forEach(D=>{Object.defineProperty(X,D,{get:()=>n[D],set:oe=>n[D]=oe})})}else e.exposed||(e.exposed={});M&&e.render===ke&&(e.render=M),E!=null&&(e.inheritAttrs=E),_&&(e.components=_),N&&(e.directives=N),T&&Ai(e)}function ql(e,t,n=ke){B(e)&&(e=xs(e));for(const s in e){const r=e[s];let i;se(r)?"default"in r?i=_t(r.from||s,r.default,!0):i=_t(r.from||s):i=_t(r),ae(i)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>i.value,set:o=>i.value=o}):t[s]=i}}function fr(e,t,n){De(B(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function Ni(e,t,n,s){let r=s.includes(".")?zi(n,s):()=>n[s];if(le(e)){const i=t[e];G(i)&&Le(r,i)}else if(G(e))Le(r,e.bind(n));else if(se(e))if(B(e))e.forEach(i=>Ni(i,t,n,s));else{const i=G(e.handler)?e.handler.bind(n):t[e.handler];G(i)&&Le(r,i,e)}}function Fi(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:o}}=e.appContext,l=i.get(t);let c;return l?c=l:!r.length&&!n&&!s?c=t:(c={},r.length&&r.forEach(u=>An(c,u,o,!0)),An(c,t,o)),se(t)&&i.set(t,c),c}function An(e,t,n,s=!1){const{mixins:r,extends:i}=t;i&&An(e,i,n,!0),r&&r.forEach(o=>An(e,o,n,!0));for(const o in t)if(!(s&&o==="expose")){const l=Gl[o]||n&&n[o];e[o]=l?l(e[o],t[o]):t[o]}return e}const Gl={data:ur,props:dr,emits:dr,methods:$t,computed:$t,beforeCreate:be,created:be,beforeMount:be,mounted:be,beforeUpdate:be,updated:be,beforeDestroy:be,beforeUnmount:be,destroyed:be,unmounted:be,activated:be,deactivated:be,errorCaptured:be,serverPrefetch:be,components:$t,directives:$t,watch:Yl,provide:ur,inject:Xl};function ur(e,t){return t?e?function(){return ue(G(e)?e.call(this,this):e,G(t)?t.call(this,this):t)}:t:e}function Xl(e,t){return $t(xs(e),xs(t))}function xs(e){if(B(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function be(e,t){return e?[...new Set([].concat(e,t))]:t}function $t(e,t){return e?ue(Object.create(null),e,t):t}function dr(e,t){return e?B(e)&&B(t)?[...new Set([...e,...t])]:ue(Object.create(null),ar(e),ar(t??{})):t}function Yl(e,t){if(!e)return t;if(!t)return e;const n=ue(Object.create(null),e);for(const s in t)n[s]=be(e[s],t[s]);return n}function Hi(){return{app:null,config:{isNativeTag:Ao,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let zl=0;function Jl(e,t){return function(s,r=null){G(s)||(s=ue({},s)),r!=null&&!se(r)&&(r=null);const i=Hi(),o=new WeakSet,l=[];let c=!1;const u=i.app={_uid:zl++,_component:s,_props:r,_container:null,_context:i,_instance:null,version:Rc,get config(){return i.config},set config(a){},use(a,...h){return o.has(a)||(a&&G(a.install)?(o.add(a),a.install(u,...h)):G(a)&&(o.add(a),a(u,...h))),u},mixin(a){return i.mixins.includes(a)||i.mixins.push(a),u},component(a,h){return h?(i.components[a]=h,u):i.components[a]},directive(a,h){return h?(i.directives[a]=h,u):i.directives[a]},mount(a,h,v){if(!c){const y=u._ceVNode||ge(s,r);return y.appContext=i,v===!0?v="svg":v===!1&&(v=void 0),h&&t?t(y,a):e(y,a,v),c=!0,u._container=a,a.__vue_app__=u,Gs(y.component)}},onUnmount(a){l.push(a)},unmount(){c&&(De(l,u._instance,16),e(null,u._container),delete u._container.__vue_app__)},provide(a,h){return i.provides[a]=h,u},runWithContext(a){const h=yt;yt=u;try{return a()}finally{yt=h}}};return u}}let yt=null;function Ql(e,t){if(fe){let n=fe.provides;const s=fe.parent&&fe.parent.provides;s===n&&(n=fe.provides=Object.create(s)),n[e]=t}}function _t(e,t,n=!1){const s=fe||pe;if(s||yt){let r=yt?yt._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&G(t)?t.call(s&&s.proxy):t}}function Di(){return!!(fe||pe||yt)}const $i={},ji=()=>Object.create($i),Vi=e=>Object.getPrototypeOf(e)===$i;function Zl(e,t,n,s=!1){const r={},i=ji();e.propsDefaults=Object.create(null),Wi(e,t,r,i);for(const o in e.propsOptions[0])o in r||(r[o]=void 0);n?e.props=s?r:cl(r):e.type.props?e.props=r:e.props=i,e.attrs=i}function ec(e,t,n,s){const{props:r,attrs:i,vnode:{patchFlag:o}}=e,l=J(r),[c]=e.propsOptions;let u=!1;if((s||o>0)&&!(o&16)){if(o&8){const a=e.vnode.dynamicProps;for(let h=0;h<a.length;h++){let v=a[h];if(kn(e.emitsOptions,v))continue;const y=t[v];if(c)if(Q(i,v))y!==i[v]&&(i[v]=y,u=!0);else{const A=Ne(v);r[A]=Ts(c,l,A,y,e,!1)}else y!==i[v]&&(i[v]=y,u=!0)}}}else{Wi(e,t,r,i)&&(u=!0);let a;for(const h in l)(!t||!Q(t,h)&&((a=lt(h))===h||!Q(t,a)))&&(c?n&&(n[h]!==void 0||n[a]!==void 0)&&(r[h]=Ts(c,l,h,void 0,e,!0)):delete r[h]);if(i!==l)for(const h in i)(!t||!Q(t,h))&&(delete i[h],u=!0)}u&&qe(e.attrs,"set","")}function Wi(e,t,n,s){const[r,i]=e.propsOptions;let o=!1,l;if(t)for(let c in t){if(Ot(c))continue;const u=t[c];let a;r&&Q(r,a=Ne(c))?!i||!i.includes(a)?n[a]=u:(l||(l={}))[a]=u:kn(e.emitsOptions,c)||(!(c in s)||u!==s[c])&&(s[c]=u,o=!0)}if(i){const c=J(n),u=l||ne;for(let a=0;a<i.length;a++){const h=i[a];n[h]=Ts(r,c,h,u[h],e,!Q(u,h))}}return o}function Ts(e,t,n,s,r,i){const o=e[n];if(o!=null){const l=Q(o,"default");if(l&&s===void 0){const c=o.default;if(o.type!==Function&&!o.skipFactory&&G(c)){const{propsDefaults:u}=r;if(n in u)s=u[n];else{const a=tn(r);s=u[n]=c.call(null,t),a()}}else s=c;r.ce&&r.ce._setProp(n,s)}o[0]&&(i&&!l?s=!1:o[1]&&(s===""||s===lt(n))&&(s=!0))}return s}const tc=new WeakMap;function ki(e,t,n=!1){const s=n?tc:t.propsCache,r=s.get(e);if(r)return r;const i=e.props,o={},l=[];let c=!1;if(!G(e)){const a=h=>{c=!0;const[v,y]=ki(h,t,!0);ue(o,v),y&&l.push(...y)};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}if(!i&&!c)return se(e)&&s.set(e,At),At;if(B(i))for(let a=0;a<i.length;a++){const h=Ne(i[a]);hr(h)&&(o[h]=ne)}else if(i)for(const a in i){const h=Ne(a);if(hr(h)){const v=i[a],y=o[h]=B(v)||G(v)?{type:v}:ue({},v),A=y.type;let P=!1,K=!0;if(B(A))for(let H=0;H<A.length;++H){const k=A[H],p=G(k)&&k.name;if(p==="Boolean"){P=!0;break}else p==="String"&&(K=!1)}else P=G(A)&&A.name==="Boolean";y[0]=P,y[1]=K,(P||Q(y,"default"))&&l.push(h)}}const u=[o,l];return se(e)&&s.set(e,u),u}function hr(e){return e[0]!=="$"&&!Ot(e)}const Bs=e=>e[0]==="_"||e==="$stable",Ks=e=>B(e)?e.map(Me):[Me(e)],nc=(e,t,n)=>{if(t._n)return t;const s=Tl((...r)=>Ks(t(...r)),n);return s._c=!1,s},Ui=(e,t,n)=>{const s=e._ctx;for(const r in e){if(Bs(r))continue;const i=e[r];if(G(i))t[r]=nc(r,i,s);else if(i!=null){const o=Ks(i);t[r]=()=>o}}},Bi=(e,t)=>{const n=Ks(t);e.slots.default=()=>n},Ki=(e,t,n)=>{for(const s in t)(n||!Bs(s))&&(e[s]=t[s])},sc=(e,t,n)=>{const s=e.slots=ji();if(e.vnode.shapeFlag&32){const r=t.__;r&&ms(s,"__",r,!0);const i=t._;i?(Ki(s,t,n),n&&ms(s,"_",i,!0)):Ui(t,s)}else t&&Bi(e,t)},rc=(e,t,n)=>{const{vnode:s,slots:r}=e;let i=!0,o=ne;if(s.shapeFlag&32){const l=t._;l?n&&l===1?i=!1:Ki(r,t,n):(i=!t.$stable,Ui(t,r)),o=t}else t&&(Bi(e,t),o={default:1});if(i)for(const l in r)!Bs(l)&&o[l]==null&&delete r[l]},Ce=eo;function ic(e){return oc(e,Il)}function oc(e,t){const n=Pn();n.__VUE__=!0;const{insert:s,remove:r,patchProp:i,createElement:o,createText:l,createComment:c,setText:u,setElementText:a,parentNode:h,nextSibling:v,setScopeId:y=ke,insertStaticContent:A}=e,P=(f,d,m,S=null,b=null,w=null,L=void 0,O=null,C=!!d.dynamicChildren)=>{if(f===d)return;f&&!ht(f,d)&&(S=sn(f),$e(f,b,w,!0),f=null),d.patchFlag===-2&&(C=!1,d.dynamicChildren=null);const{type:x,ref:V,shapeFlag:F}=d;switch(x){case bt:K(f,d,m,S);break;case he:H(f,d,m,S);break;case kt:f==null&&k(d,m,S,L);break;case Se:_(f,d,m,S,b,w,L,O,C);break;default:F&1?M(f,d,m,S,b,w,L,O,C):F&6?N(f,d,m,S,b,w,L,O,C):(F&64||F&128)&&x.process(f,d,m,S,b,w,L,O,C,xt)}V!=null&&b?It(V,f&&f.ref,w,d||f,!d):V==null&&f&&f.ref!=null&&It(f.ref,null,w,f,!0)},K=(f,d,m,S)=>{if(f==null)s(d.el=l(d.children),m,S);else{const b=d.el=f.el;d.children!==f.children&&u(b,d.children)}},H=(f,d,m,S)=>{f==null?s(d.el=c(d.children||""),m,S):d.el=f.el},k=(f,d,m,S)=>{[f.el,f.anchor]=A(f.children,d,m,S,f.el,f.anchor)},p=({el:f,anchor:d},m,S)=>{let b;for(;f&&f!==d;)b=v(f),s(f,m,S),f=b;s(d,m,S)},g=({el:f,anchor:d})=>{let m;for(;f&&f!==d;)m=v(f),r(f),f=m;r(d)},M=(f,d,m,S,b,w,L,O,C)=>{d.type==="svg"?L="svg":d.type==="math"&&(L="mathml"),f==null?W(d,m,S,b,w,L,O,C):T(f,d,b,w,L,O,C)},W=(f,d,m,S,b,w,L,O)=>{let C,x;const{props:V,shapeFlag:F,transition:$,dirs:q}=f;if(C=f.el=o(f.type,w,V&&V.is,V),F&8?a(C,f.children):F&16&&U(f.children,C,null,S,b,es(f,w),L,O),q&&We(f,null,S,"created"),R(C,f,f.scopeId,L,S),V){for(const ee in V)ee!=="value"&&!Ot(ee)&&i(C,ee,null,V[ee],w,S);"value"in V&&i(C,"value",null,V.value,w),(x=V.onVnodeBeforeMount)&&Oe(x,S,f)}q&&We(f,null,S,"beforeMount");const z=qi(b,$);z&&$.beforeEnter(C),s(C,d,m),((x=V&&V.onVnodeMounted)||z||q)&&Ce(()=>{x&&Oe(x,S,f),z&&$.enter(C),q&&We(f,null,S,"mounted")},b)},R=(f,d,m,S,b)=>{if(m&&y(f,m),S)for(let w=0;w<S.length;w++)y(f,S[w]);if(b){let w=b.subTree;if(d===w||Zi(w.type)&&(w.ssContent===d||w.ssFallback===d)){const L=b.vnode;R(f,L,L.scopeId,L.slotScopeIds,b.parent)}}},U=(f,d,m,S,b,w,L,O,C=0)=>{for(let x=C;x<f.length;x++){const V=f[x]=O?nt(f[x]):Me(f[x]);P(null,V,d,m,S,b,w,L,O)}},T=(f,d,m,S,b,w,L)=>{const O=d.el=f.el;let{patchFlag:C,dynamicChildren:x,dirs:V}=d;C|=f.patchFlag&16;const F=f.props||ne,$=d.props||ne;let q;if(m&&at(m,!1),(q=$.onVnodeBeforeUpdate)&&Oe(q,m,d,f),V&&We(d,f,m,"beforeUpdate"),m&&at(m,!0),(F.innerHTML&&$.innerHTML==null||F.textContent&&$.textContent==null)&&a(O,""),x?I(f.dynamicChildren,x,O,m,S,es(d,b),w):L||D(f,d,O,null,m,S,es(d,b),w,!1),C>0){if(C&16)E(O,F,$,m,b);else if(C&2&&F.class!==$.class&&i(O,"class",null,$.class,b),C&4&&i(O,"style",F.style,$.style,b),C&8){const z=d.dynamicProps;for(let ee=0;ee<z.length;ee++){const Z=z[ee],xe=F[Z],me=$[Z];(me!==xe||Z==="value")&&i(O,Z,xe,me,b,m)}}C&1&&f.children!==d.children&&a(O,d.children)}else!L&&x==null&&E(O,F,$,m,b);((q=$.onVnodeUpdated)||V)&&Ce(()=>{q&&Oe(q,m,d,f),V&&We(d,f,m,"updated")},S)},I=(f,d,m,S,b,w,L)=>{for(let O=0;O<d.length;O++){const C=f[O],x=d[O],V=C.el&&(C.type===Se||!ht(C,x)||C.shapeFlag&198)?h(C.el):m;P(C,x,V,null,S,b,w,L,!0)}},E=(f,d,m,S,b)=>{if(d!==m){if(d!==ne)for(const w in d)!Ot(w)&&!(w in m)&&i(f,w,d[w],null,b,S);for(const w in m){if(Ot(w))continue;const L=m[w],O=d[w];L!==O&&w!=="value"&&i(f,w,O,L,b,S)}"value"in m&&i(f,"value",d.value,m.value,b)}},_=(f,d,m,S,b,w,L,O,C)=>{const x=d.el=f?f.el:l(""),V=d.anchor=f?f.anchor:l("");let{patchFlag:F,dynamicChildren:$,slotScopeIds:q}=d;q&&(O=O?O.concat(q):q),f==null?(s(x,m,S),s(V,m,S),U(d.children||[],m,V,b,w,L,O,C)):F>0&&F&64&&$&&f.dynamicChildren?(I(f.dynamicChildren,$,m,b,w,L,O),(d.key!=null||b&&d===b.subTree)&&Gi(f,d,!0)):D(f,d,m,V,b,w,L,O,C)},N=(f,d,m,S,b,w,L,O,C)=>{d.slotScopeIds=O,f==null?d.shapeFlag&512?b.ctx.activate(d,m,S,L,C):Y(d,m,S,b,w,L,C):re(f,d,C)},Y=(f,d,m,S,b,w,L)=>{const O=f.component=Sc(f,S,b);if($n(f)&&(O.ctx.renderer=xt),xc(O,!1,L),O.asyncDep){if(b&&b.registerDep(O,j,L),!f.el){const C=O.subTree=ge(he);H(null,C,d,m)}}else j(O,f,d,m,b,w,L)},re=(f,d,m)=>{const S=d.component=f.component;if(gc(f,d,m))if(S.asyncDep&&!S.asyncResolved){X(S,d,m);return}else S.next=d,S.update();else d.el=f.el,S.vnode=d},j=(f,d,m,S,b,w,L)=>{const O=()=>{if(f.isMounted){let{next:F,bu:$,u:q,parent:z,vnode:ee}=f;{const Te=Xi(f);if(Te){F&&(F.el=ee.el,X(f,F,L)),Te.asyncDep.then(()=>{f.isUnmounted||O()});return}}let Z=F,xe;at(f,!1),F?(F.el=ee.el,X(f,F,L)):F=ee,$&&Gn($),(xe=F.props&&F.props.onVnodeBeforeUpdate)&&Oe(xe,z,F,ee),at(f,!0);const me=ts(f),Fe=f.subTree;f.subTree=me,P(Fe,me,h(Fe.el),sn(Fe),f,b,w),F.el=me.el,Z===null&&Qi(f,me.el),q&&Ce(q,b),(xe=F.props&&F.props.onVnodeUpdated)&&Ce(()=>Oe(xe,z,F,ee),b)}else{let F;const{el:$,props:q}=d,{bm:z,m:ee,parent:Z,root:xe,type:me}=f,Fe=vt(d);if(at(f,!1),z&&Gn(z),!Fe&&(F=q&&q.onVnodeBeforeMount)&&Oe(F,Z,d),at(f,!0),$&&qn){const Te=()=>{f.subTree=ts(f),qn($,f.subTree,f,b,null)};Fe&&me.__asyncHydrate?me.__asyncHydrate($,f,Te):Te()}else{xe.ce&&xe.ce._def.shadowRoot!==!1&&xe.ce._injectChildStyle(me);const Te=f.subTree=ts(f);P(null,Te,m,S,f,b,w),d.el=Te.el}if(ee&&Ce(ee,b),!Fe&&(F=q&&q.onVnodeMounted)){const Te=d;Ce(()=>Oe(F,Z,Te),b)}(d.shapeFlag&256||Z&&vt(Z.vnode)&&Z.vnode.shapeFlag&256)&&f.a&&Ce(f.a,b),f.isMounted=!0,d=m=S=null}};f.scope.on();const C=f.effect=new ei(O);f.scope.off();const x=f.update=C.run.bind(C),V=f.job=C.runIfDirty.bind(C);V.i=f,V.id=f.uid,C.scheduler=()=>Us(V),at(f,!0),x()},X=(f,d,m)=>{d.component=f;const S=f.vnode.props;f.vnode=d,f.next=null,ec(f,d.props,S,m),rc(f,d.children,m),Ge(),rr(f),Xe()},D=(f,d,m,S,b,w,L,O,C=!1)=>{const x=f&&f.children,V=f?f.shapeFlag:0,F=d.children,{patchFlag:$,shapeFlag:q}=d;if($>0){if($&128){nn(x,F,m,S,b,w,L,O,C);return}else if($&256){oe(x,F,m,S,b,w,L,O,C);return}}q&8?(V&16&&Nt(x,b,w),F!==x&&a(m,F)):V&16?q&16?nn(x,F,m,S,b,w,L,O,C):Nt(x,b,w,!0):(V&8&&a(m,""),q&16&&U(F,m,S,b,w,L,O,C))},oe=(f,d,m,S,b,w,L,O,C)=>{f=f||At,d=d||At;const x=f.length,V=d.length,F=Math.min(x,V);let $;for($=0;$<F;$++){const q=d[$]=C?nt(d[$]):Me(d[$]);P(f[$],q,m,null,b,w,L,O,C)}x>V?Nt(f,b,w,!0,!1,F):U(d,m,S,b,w,L,O,C,F)},nn=(f,d,m,S,b,w,L,O,C)=>{let x=0;const V=d.length;let F=f.length-1,$=V-1;for(;x<=F&&x<=$;){const q=f[x],z=d[x]=C?nt(d[x]):Me(d[x]);if(ht(q,z))P(q,z,m,null,b,w,L,O,C);else break;x++}for(;x<=F&&x<=$;){const q=f[F],z=d[$]=C?nt(d[$]):Me(d[$]);if(ht(q,z))P(q,z,m,null,b,w,L,O,C);else break;F--,$--}if(x>F){if(x<=$){const q=$+1,z=q<V?d[q].el:S;for(;x<=$;)P(null,d[x]=C?nt(d[x]):Me(d[x]),m,z,b,w,L,O,C),x++}}else if(x>$)for(;x<=F;)$e(f[x],b,w,!0),x++;else{const q=x,z=x,ee=new Map;for(x=z;x<=$;x++){const Ee=d[x]=C?nt(d[x]):Me(d[x]);Ee.key!=null&&ee.set(Ee.key,x)}let Z,xe=0;const me=$-z+1;let Fe=!1,Te=0;const Ft=new Array(me);for(x=0;x<me;x++)Ft[x]=0;for(x=q;x<=F;x++){const Ee=f[x];if(xe>=me){$e(Ee,b,w,!0);continue}let je;if(Ee.key!=null)je=ee.get(Ee.key);else for(Z=z;Z<=$;Z++)if(Ft[Z-z]===0&&ht(Ee,d[Z])){je=Z;break}je===void 0?$e(Ee,b,w,!0):(Ft[je-z]=x+1,je>=Te?Te=je:Fe=!0,P(Ee,d[je],m,null,b,w,L,O,C),xe++)}const Zs=Fe?lc(Ft):At;for(Z=Zs.length-1,x=me-1;x>=0;x--){const Ee=z+x,je=d[Ee],er=Ee+1<V?d[Ee+1].el:S;Ft[x]===0?P(null,je,m,er,b,w,L,O,C):Fe&&(Z<0||x!==Zs[Z]?ct(je,m,er,2):Z--)}}},ct=(f,d,m,S,b=null)=>{const{el:w,type:L,transition:O,children:C,shapeFlag:x}=f;if(x&6){ct(f.component.subTree,d,m,S);return}if(x&128){f.suspense.move(d,m,S);return}if(x&64){L.move(f,d,m,xt);return}if(L===Se){s(w,d,m);for(let F=0;F<C.length;F++)ct(C[F],d,m,S);s(f.anchor,d,m);return}if(L===kt){p(f,d,m);return}if(S!==2&&x&1&&O)if(S===0)O.beforeEnter(w),s(w,d,m),Ce(()=>O.enter(w),b);else{const{leave:F,delayLeave:$,afterLeave:q}=O,z=()=>{f.ctx.isUnmounted?r(w):s(w,d,m)},ee=()=>{F(w,()=>{z(),q&&q()})};$?$(w,z,ee):ee()}else s(w,d,m)},$e=(f,d,m,S=!1,b=!1)=>{const{type:w,props:L,ref:O,children:C,dynamicChildren:x,shapeFlag:V,patchFlag:F,dirs:$,cacheIndex:q}=f;if(F===-2&&(b=!1),O!=null&&(Ge(),It(O,null,m,f,!0),Xe()),q!=null&&(d.renderCache[q]=void 0),V&256){d.ctx.deactivate(f);return}const z=V&1&&$,ee=!vt(f);let Z;if(ee&&(Z=L&&L.onVnodeBeforeUnmount)&&Oe(Z,d,f),V&6)Co(f.component,m,S);else{if(V&128){f.suspense.unmount(m,S);return}z&&We(f,null,d,"beforeUnmount"),V&64?f.type.remove(f,d,m,xt,S):x&&!x.hasOnce&&(w!==Se||F>0&&F&64)?Nt(x,d,m,!1,!0):(w===Se&&F&384||!b&&V&16)&&Nt(C,d,m),S&&Js(f)}(ee&&(Z=L&&L.onVnodeUnmounted)||z)&&Ce(()=>{Z&&Oe(Z,d,f),z&&We(f,null,d,"unmounted")},m)},Js=f=>{const{type:d,el:m,anchor:S,transition:b}=f;if(d===Se){Eo(m,S);return}if(d===kt){g(f);return}const w=()=>{r(m),b&&!b.persisted&&b.afterLeave&&b.afterLeave()};if(f.shapeFlag&1&&b&&!b.persisted){const{leave:L,delayLeave:O}=b,C=()=>L(m,w);O?O(f.el,w,C):C()}else w()},Eo=(f,d)=>{let m;for(;f!==d;)m=v(f),r(f),f=m;r(d)},Co=(f,d,m)=>{const{bum:S,scope:b,job:w,subTree:L,um:O,m:C,a:x,parent:V,slots:{__:F}}=f;pr(C),pr(x),S&&Gn(S),V&&B(F)&&F.forEach($=>{V.renderCache[$]=void 0}),b.stop(),w&&(w.flags|=8,$e(L,f,d,m)),O&&Ce(O,d),Ce(()=>{f.isUnmounted=!0},d),d&&d.pendingBranch&&!d.isUnmounted&&f.asyncDep&&!f.asyncResolved&&f.suspenseId===d.pendingId&&(d.deps--,d.deps===0&&d.resolve())},Nt=(f,d,m,S=!1,b=!1,w=0)=>{for(let L=w;L<f.length;L++)$e(f[L],d,m,S,b)},sn=f=>{if(f.shapeFlag&6)return sn(f.component.subTree);if(f.shapeFlag&128)return f.suspense.next();const d=v(f.anchor||f.el),m=d&&d[El];return m?v(m):d};let Bn=!1;const Qs=(f,d,m)=>{f==null?d._vnode&&$e(d._vnode,null,null,!0):P(d._vnode||null,f,d,null,null,null,m),d._vnode=f,Bn||(Bn=!0,rr(),En(),Bn=!1)},xt={p:P,um:$e,m:ct,r:Js,mt:Y,mc:U,pc:D,pbc:I,n:sn,o:e};let Kn,qn;return t&&([Kn,qn]=t(xt)),{render:Qs,hydrate:Kn,createApp:Jl(Qs,Kn)}}function es({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function at({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function qi(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Gi(e,t,n=!1){const s=e.children,r=t.children;if(B(s)&&B(r))for(let i=0;i<s.length;i++){const o=s[i];let l=r[i];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=r[i]=nt(r[i]),l.el=o.el),!n&&l.patchFlag!==-2&&Gi(o,l)),l.type===bt&&(l.el=o.el),l.type===he&&!l.el&&(l.el=o.el)}}function lc(e){const t=e.slice(),n=[0];let s,r,i,o,l;const c=e.length;for(s=0;s<c;s++){const u=e[s];if(u!==0){if(r=n[n.length-1],e[r]<u){t[s]=r,n.push(s);continue}for(i=0,o=n.length-1;i<o;)l=i+o>>1,e[n[l]]<u?i=l+1:o=l;u<e[n[i]]&&(i>0&&(t[s]=n[i-1]),n[i]=s)}}for(i=n.length,o=n[i-1];i-- >0;)n[i]=o,o=t[o];return n}function Xi(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Xi(t)}function pr(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const cc=Symbol.for("v-scx"),ac=()=>_t(cc);function Yi(e,t){return Wn(e,null,t)}function mf(e,t){return Wn(e,null,{flush:"post"})}function Le(e,t,n){return Wn(e,t,n)}function Wn(e,t,n=ne){const{immediate:s,deep:r,flush:i,once:o}=n,l=ue({},n),c=t&&s||!t&&i!=="post";let u;if(zt){if(i==="sync"){const y=ac();u=y.__watcherHandles||(y.__watcherHandles=[])}else if(!c){const y=()=>{};return y.stop=ke,y.resume=ke,y.pause=ke,y}}const a=fe;l.call=(y,A,P)=>De(y,a,A,P);let h=!1;i==="post"?l.scheduler=y=>{Ce(y,a&&a.suspense)}:i!=="sync"&&(h=!0,l.scheduler=(y,A)=>{A?y():Us(y)}),l.augmentJob=y=>{t&&(y.flags|=4),h&&(y.flags|=2,a&&(y.id=a.uid,y.i=a))};const v=bl(e,t,l);return zt&&(u?u.push(v):c&&v()),v}function fc(e,t,n){const s=this.proxy,r=le(e)?e.includes(".")?zi(s,e):()=>s[e]:e.bind(s,s);let i;G(t)?i=t:(i=t.handler,n=t);const o=tn(this),l=Wn(r,i.bind(s),n);return o(),l}function zi(e,t){const n=t.split(".");return()=>{let s=e;for(let r=0;r<n.length&&s;r++)s=s[n[r]];return s}}const uc=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Ne(t)}Modifiers`]||e[`${lt(t)}Modifiers`];function dc(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||ne;let r=n;const i=t.startsWith("update:"),o=i&&uc(s,t.slice(7));o&&(o.trim&&(r=n.map(a=>le(a)?a.trim():a)),o.number&&(r=n.map(Po)));let l,c=s[l=mn(t)]||s[l=mn(Ne(t))];!c&&i&&(c=s[l=mn(lt(t))]),c&&De(c,e,6,r);const u=s[l+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,De(u,e,6,r)}}function Ji(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(r!==void 0)return r;const i=e.emits;let o={},l=!1;if(!G(e)){const c=u=>{const a=Ji(u,t,!0);a&&(l=!0,ue(o,a))};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!i&&!l?(se(e)&&s.set(e,null),null):(B(i)?i.forEach(c=>o[c]=null):ue(o,i),se(e)&&s.set(e,o),o)}function kn(e,t){return!e||!Qt(t)?!1:(t=t.slice(2).replace(/Once$/,""),Q(e,t[0].toLowerCase()+t.slice(1))||Q(e,lt(t))||Q(e,t))}function ts(e){const{type:t,vnode:n,proxy:s,withProxy:r,propsOptions:[i],slots:o,attrs:l,emit:c,render:u,renderCache:a,props:h,data:v,setupState:y,ctx:A,inheritAttrs:P}=e,K=Cn(e);let H,k;try{if(n.shapeFlag&4){const g=r||s,M=g;H=Me(u.call(M,g,a,h,y,v,A)),k=l}else{const g=t;H=Me(g.length>1?g(h,{attrs:l,slots:o,emit:c}):g(h,null)),k=t.props?l:hc(l)}}catch(g){Ut.length=0,Hn(g,e,1),H=ge(he)}let p=H;if(k&&P!==!1){const g=Object.keys(k),{shapeFlag:M}=p;g.length&&M&7&&(i&&g.some(Is)&&(k=pc(k,i)),p=ot(p,k,!1,!0))}return n.dirs&&(p=ot(p,null,!1,!0),p.dirs=p.dirs?p.dirs.concat(n.dirs):n.dirs),n.transition&&Gt(p,n.transition),H=p,Cn(K),H}const hc=e=>{let t;for(const n in e)(n==="class"||n==="style"||Qt(n))&&((t||(t={}))[n]=e[n]);return t},pc=(e,t)=>{const n={};for(const s in e)(!Is(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function gc(e,t,n){const{props:s,children:r,component:i}=e,{props:o,children:l,patchFlag:c}=t,u=i.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&c>=0){if(c&1024)return!0;if(c&16)return s?gr(s,o,u):!!o;if(c&8){const a=t.dynamicProps;for(let h=0;h<a.length;h++){const v=a[h];if(o[v]!==s[v]&&!kn(u,v))return!0}}}else return(r||l)&&(!l||!l.$stable)?!0:s===o?!1:s?o?gr(s,o,u):!0:!!o;return!1}function gr(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const i=s[r];if(t[i]!==e[i]&&!kn(n,i))return!0}return!1}function Qi({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const Zi=e=>e.__isSuspense;function eo(e,t){t&&t.pendingBranch?B(e)?t.effects.push(...e):t.effects.push(e):xl(e)}const Se=Symbol.for("v-fgt"),bt=Symbol.for("v-txt"),he=Symbol.for("v-cmt"),kt=Symbol.for("v-stc"),Ut=[];let Ae=null;function Es(e=!1){Ut.push(Ae=e?null:[])}function mc(){Ut.pop(),Ae=Ut[Ut.length-1]||null}let Xt=1;function mr(e,t=!1){Xt+=e,e<0&&Ae&&t&&(Ae.hasOnce=!0)}function to(e){return e.dynamicChildren=Xt>0?Ae||At:null,mc(),Xt>0&&Ae&&Ae.push(e),e}function vf(e,t,n,s,r,i){return to(so(e,t,n,s,r,i,!0))}function Cs(e,t,n,s,r){return to(ge(e,t,n,s,r,!0))}function Yt(e){return e?e.__v_isVNode===!0:!1}function ht(e,t){return e.type===t.type&&e.key===t.key}const no=({key:e})=>e??null,yn=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?le(e)||ae(e)||G(e)?{i:pe,r:e,k:t,f:!!n}:e:null);function so(e,t=null,n=null,s=0,r=null,i=e===Se?0:1,o=!1,l=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&no(t),ref:t&&yn(t),scopeId:_i,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:pe};return l?(qs(c,n),i&128&&e.normalize(c)):n&&(c.shapeFlag|=le(n)?8:16),Xt>0&&!o&&Ae&&(c.patchFlag>0||i&6)&&c.patchFlag!==32&&Ae.push(c),c}const ge=vc;function vc(e,t=null,n=null,s=0,r=null,i=!1){if((!e||e===Ii)&&(e=he),Yt(e)){const l=ot(e,t,!0);return n&&qs(l,n),Xt>0&&!i&&Ae&&(l.shapeFlag&6?Ae[Ae.indexOf(e)]=l:Ae.push(l)),l.patchFlag=-2,l}if(Ac(e)&&(e=e.__vccOpts),t){t=yc(t);let{class:l,style:c}=t;l&&!le(l)&&(t.class=Fs(l)),se(c)&&(Ws(c)&&!B(c)&&(c=ue({},c)),t.style=Ns(c))}const o=le(e)?1:Zi(e)?128:bi(e)?64:se(e)?4:G(e)?2:0;return so(e,t,n,s,r,o,i,!0)}function yc(e){return e?Ws(e)||Vi(e)?ue({},e):e:null}function ot(e,t,n=!1,s=!1){const{props:r,ref:i,patchFlag:o,children:l,transition:c}=e,u=t?_c(r||{},t):r,a={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&no(u),ref:t&&t.ref?n&&i?B(i)?i.concat(yn(t)):[i,yn(t)]:yn(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Se?o===-1?16:o|16:o,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&ot(e.ssContent),ssFallback:e.ssFallback&&ot(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&s&&Gt(a,c.clone(a)),a}function ro(e=" ",t=0){return ge(bt,null,e,t)}function yf(e,t){const n=ge(kt,null,e);return n.staticCount=t,n}function _f(e="",t=!1){return t?(Es(),Cs(he,null,e)):ge(he,null,e)}function Me(e){return e==null||typeof e=="boolean"?ge(he):B(e)?ge(Se,null,e.slice()):Yt(e)?nt(e):ge(bt,null,String(e))}function nt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:ot(e)}function qs(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(B(t))n=16;else if(typeof t=="object")if(s&65){const r=t.default;r&&(r._c&&(r._d=!1),qs(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!Vi(t)?t._ctx=pe:r===3&&pe&&(pe.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else G(t)?(t={default:t,_ctx:pe},n=32):(t=String(t),s&64?(n=16,t=[ro(t)]):n=8);e.children=t,e.shapeFlag|=n}function _c(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const r in s)if(r==="class")t.class!==s.class&&(t.class=Fs([t.class,s.class]));else if(r==="style")t.style=Ns([t.style,s.style]);else if(Qt(r)){const i=t[r],o=s[r];o&&i!==o&&!(B(i)&&i.includes(o))&&(t[r]=i?[].concat(i,o):o)}else r!==""&&(t[r]=s[r])}return t}function Oe(e,t,n,s=null){De(e,t,7,[n,s])}const bc=Hi();let wc=0;function Sc(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||bc,i={uid:wc++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Wo(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:ki(s,r),emitsOptions:Ji(s,r),emit:null,emitted:null,propsDefaults:ne,inheritAttrs:s.inheritAttrs,ctx:ne,data:ne,props:ne,attrs:ne,slots:ne,refs:ne,setupState:ne,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=dc.bind(null,i),e.ce&&e.ce(i),i}let fe=null;const en=()=>fe||pe;let Rn,As;{const e=Pn(),t=(n,s)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(s),i=>{r.length>1?r.forEach(o=>o(i)):r[0](i)}};Rn=t("__VUE_INSTANCE_SETTERS__",n=>fe=n),As=t("__VUE_SSR_SETTERS__",n=>zt=n)}const tn=e=>{const t=fe;return Rn(e),e.scope.on(),()=>{e.scope.off(),Rn(t)}},vr=()=>{fe&&fe.scope.off(),Rn(null)};function io(e){return e.vnode.shapeFlag&4}let zt=!1;function xc(e,t=!1,n=!1){t&&As(t);const{props:s,children:r}=e.vnode,i=io(e);Zl(e,s,i,t),sc(e,r,n||t);const o=i?Tc(e,t):void 0;return t&&As(!1),o}function Tc(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Ul);const{setup:s}=n;if(s){Ge();const r=e.setupContext=s.length>1?lo(e):null,i=tn(e),o=Zt(s,e,0,[e.props,r]),l=Gr(o);if(Xe(),i(),(l||e.sp)&&!vt(e)&&Ai(e),l){if(o.then(vr,vr),t)return o.then(c=>{yr(e,c)}).catch(c=>{Hn(c,e,0)});e.asyncDep=o}else yr(e,o)}else oo(e)}function yr(e,t,n){G(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:se(t)&&(e.setupState=gi(t)),oo(e)}function oo(e,t,n){const s=e.type;e.render||(e.render=s.render||ke);{const r=tn(e);Ge();try{Kl(e)}finally{Xe(),r()}}}const Ec={get(e,t){return _e(e,"get",""),e[t]}};function lo(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Ec),slots:e.slots,emit:e.emit,expose:t}}function Gs(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(gi(vn(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Wt)return Wt[n](e)},has(t,n){return n in t||n in Wt}})):e.proxy}function Cc(e,t=!0){return G(e)?e.displayName||e.name:e.name||t&&e.__name}function Ac(e){return G(e)&&"__vccOpts"in e}const ie=(e,t)=>yl(e,t,zt);function Rs(e,t,n){const s=arguments.length;return s===2?se(t)&&!B(t)?Yt(t)?ge(e,null,[t]):ge(e,t):ge(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&Yt(n)&&(n=[n]),ge(e,t,n))}const Rc="3.5.17";/**
* @vue/runtime-dom v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Os;const _r=typeof window<"u"&&window.trustedTypes;if(_r)try{Os=_r.createPolicy("vue",{createHTML:e=>e})}catch{}const co=Os?e=>Os.createHTML(e):e=>e,Oc="http://www.w3.org/2000/svg",Mc="http://www.w3.org/1998/Math/MathML",Ke=typeof document<"u"?document:null,br=Ke&&Ke.createElement("template"),Ic={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r=t==="svg"?Ke.createElementNS(Oc,e):t==="mathml"?Ke.createElementNS(Mc,e):n?Ke.createElement(e,{is:n}):Ke.createElement(e);return e==="select"&&s&&s.multiple!=null&&r.setAttribute("multiple",s.multiple),r},createText:e=>Ke.createTextNode(e),createComment:e=>Ke.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Ke.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,i){const o=n?n.previousSibling:t.lastChild;if(r&&(r===i||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===i||!(r=r.nextSibling)););else{br.innerHTML=co(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const l=br.content;if(s==="svg"||s==="mathml"){const c=l.firstChild;for(;c.firstChild;)l.appendChild(c.firstChild);l.removeChild(c)}t.insertBefore(l,n)}return[o?o.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Ze="transition",Dt="animation",Jt=Symbol("_vtc"),ao={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},Pc=ue({},wi,ao),Lc=e=>(e.displayName="Transition",e.props=Pc,e),bf=Lc((e,{slots:t})=>Rs(Rl,Nc(e),t)),ft=(e,t=[])=>{B(e)?e.forEach(n=>n(...t)):e&&e(...t)},wr=e=>e?B(e)?e.some(t=>t.length>1):e.length>1:!1;function Nc(e){const t={};for(const _ in e)_ in ao||(t[_]=e[_]);if(e.css===!1)return t;const{name:n="v",type:s,duration:r,enterFromClass:i=`${n}-enter-from`,enterActiveClass:o=`${n}-enter-active`,enterToClass:l=`${n}-enter-to`,appearFromClass:c=i,appearActiveClass:u=o,appearToClass:a=l,leaveFromClass:h=`${n}-leave-from`,leaveActiveClass:v=`${n}-leave-active`,leaveToClass:y=`${n}-leave-to`}=e,A=Fc(r),P=A&&A[0],K=A&&A[1],{onBeforeEnter:H,onEnter:k,onEnterCancelled:p,onLeave:g,onLeaveCancelled:M,onBeforeAppear:W=H,onAppear:R=k,onAppearCancelled:U=p}=t,T=(_,N,Y,re)=>{_._enterCancelled=re,ut(_,N?a:l),ut(_,N?u:o),Y&&Y()},I=(_,N)=>{_._isLeaving=!1,ut(_,h),ut(_,y),ut(_,v),N&&N()},E=_=>(N,Y)=>{const re=_?R:k,j=()=>T(N,_,Y);ft(re,[N,j]),Sr(()=>{ut(N,_?c:i),Be(N,_?a:l),wr(re)||xr(N,s,P,j)})};return ue(t,{onBeforeEnter(_){ft(H,[_]),Be(_,i),Be(_,o)},onBeforeAppear(_){ft(W,[_]),Be(_,c),Be(_,u)},onEnter:E(!1),onAppear:E(!0),onLeave(_,N){_._isLeaving=!0;const Y=()=>I(_,N);Be(_,h),_._enterCancelled?(Be(_,v),Cr()):(Cr(),Be(_,v)),Sr(()=>{_._isLeaving&&(ut(_,h),Be(_,y),wr(g)||xr(_,s,K,Y))}),ft(g,[_,Y])},onEnterCancelled(_){T(_,!1,void 0,!0),ft(p,[_])},onAppearCancelled(_){T(_,!0,void 0,!0),ft(U,[_])},onLeaveCancelled(_){I(_),ft(M,[_])}})}function Fc(e){if(e==null)return null;if(se(e))return[ns(e.enter),ns(e.leave)];{const t=ns(e);return[t,t]}}function ns(e){return Lo(e)}function Be(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[Jt]||(e[Jt]=new Set)).add(t)}function ut(e,t){t.split(/\s+/).forEach(s=>s&&e.classList.remove(s));const n=e[Jt];n&&(n.delete(t),n.size||(e[Jt]=void 0))}function Sr(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Hc=0;function xr(e,t,n,s){const r=e._endId=++Hc,i=()=>{r===e._endId&&s()};if(n!=null)return setTimeout(i,n);const{type:o,timeout:l,propCount:c}=Dc(e,t);if(!o)return s();const u=o+"end";let a=0;const h=()=>{e.removeEventListener(u,v),i()},v=y=>{y.target===e&&++a>=c&&h()};setTimeout(()=>{a<c&&h()},l+1),e.addEventListener(u,v)}function Dc(e,t){const n=window.getComputedStyle(e),s=A=>(n[A]||"").split(", "),r=s(`${Ze}Delay`),i=s(`${Ze}Duration`),o=Tr(r,i),l=s(`${Dt}Delay`),c=s(`${Dt}Duration`),u=Tr(l,c);let a=null,h=0,v=0;t===Ze?o>0&&(a=Ze,h=o,v=i.length):t===Dt?u>0&&(a=Dt,h=u,v=c.length):(h=Math.max(o,u),a=h>0?o>u?Ze:Dt:null,v=a?a===Ze?i.length:c.length:0);const y=a===Ze&&/\b(transform|all)(,|$)/.test(s(`${Ze}Property`).toString());return{type:a,timeout:h,propCount:v,hasTransform:y}}function Tr(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,s)=>Er(n)+Er(e[s])))}function Er(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function Cr(){return document.body.offsetHeight}function $c(e,t,n){const s=e[Jt];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Ar=Symbol("_vod"),jc=Symbol("_vsh"),Vc=Symbol(""),Wc=/(^|;)\s*display\s*:/;function kc(e,t,n){const s=e.style,r=le(n);let i=!1;if(n&&!r){if(t)if(le(t))for(const o of t.split(";")){const l=o.slice(0,o.indexOf(":")).trim();n[l]==null&&_n(s,l,"")}else for(const o in t)n[o]==null&&_n(s,o,"");for(const o in n)o==="display"&&(i=!0),_n(s,o,n[o])}else if(r){if(t!==n){const o=s[Vc];o&&(n+=";"+o),s.cssText=n,i=Wc.test(n)}}else t&&e.removeAttribute("style");Ar in e&&(e[Ar]=i?s.display:"",e[jc]&&(s.display="none"))}const Rr=/\s*!important$/;function _n(e,t,n){if(B(n))n.forEach(s=>_n(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=Uc(e,t);Rr.test(n)?e.setProperty(lt(s),n.replace(Rr,""),"important"):e[s]=n}}const Or=["Webkit","Moz","ms"],ss={};function Uc(e,t){const n=ss[t];if(n)return n;let s=Ne(t);if(s!=="filter"&&s in e)return ss[t]=s;s=In(s);for(let r=0;r<Or.length;r++){const i=Or[r]+s;if(i in e)return ss[t]=i}return t}const Mr="http://www.w3.org/1999/xlink";function Ir(e,t,n,s,r,i=jo(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(Mr,t.slice(6,t.length)):e.setAttributeNS(Mr,t,n):n==null||i&&!zr(n)?e.removeAttribute(t):e.setAttribute(t,i?"":Je(n)?String(n):n)}function Pr(e,t,n,s,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?co(n):n);return}const i=e.tagName;if(t==="value"&&i!=="PROGRESS"&&!i.includes("-")){const l=i==="OPTION"?e.getAttribute("value")||"":e.value,c=n==null?e.type==="checkbox"?"on":"":String(n);(l!==c||!("_value"in e))&&(e.value=c),n==null&&e.removeAttribute(t),e._value=n;return}let o=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=zr(n):n==null&&l==="string"?(n="",o=!0):l==="number"&&(n=0,o=!0)}try{e[t]=n}catch{}o&&e.removeAttribute(r||t)}function Bc(e,t,n,s){e.addEventListener(t,n,s)}function Kc(e,t,n,s){e.removeEventListener(t,n,s)}const Lr=Symbol("_vei");function qc(e,t,n,s,r=null){const i=e[Lr]||(e[Lr]={}),o=i[t];if(s&&o)o.value=s;else{const[l,c]=Gc(t);if(s){const u=i[t]=zc(s,r);Bc(e,l,u,c)}else o&&(Kc(e,l,o,c),i[t]=void 0)}}const Nr=/(?:Once|Passive|Capture)$/;function Gc(e){let t;if(Nr.test(e)){t={};let s;for(;s=e.match(Nr);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):lt(e.slice(2)),t]}let rs=0;const Xc=Promise.resolve(),Yc=()=>rs||(Xc.then(()=>rs=0),rs=Date.now());function zc(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;De(Jc(s,n.value),t,5,[s])};return n.value=e,n.attached=Yc(),n}function Jc(e,t){if(B(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>r=>!r._stopped&&s&&s(r))}else return t}const Fr=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Qc=(e,t,n,s,r,i)=>{const o=r==="svg";t==="class"?$c(e,s,o):t==="style"?kc(e,n,s):Qt(t)?Is(t)||qc(e,t,n,s,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Zc(e,t,s,o))?(Pr(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Ir(e,t,s,o,i,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!le(s))?Pr(e,Ne(t),s,i,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),Ir(e,t,s,o))};function Zc(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&Fr(t)&&G(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return Fr(t)&&le(n)?!1:t in e}const ea=["ctrl","shift","alt","meta"],ta={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>ea.some(n=>e[`${n}Key`]&&!t.includes(n))},wf=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=(r,...i)=>{for(let o=0;o<t.length;o++){const l=ta[t[o]];if(l&&l(r,t))return}return e(r,...i)})},na={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},Sf=(e,t)=>{const n=e._withKeys||(e._withKeys={}),s=t.join(".");return n[s]||(n[s]=r=>{if(!("key"in r))return;const i=lt(r.key);if(t.some(o=>o===i||na[o]===i))return e(r)})},sa=ue({patchProp:Qc},Ic);let is,Hr=!1;function ra(){return is=Hr?is:ic(sa),Hr=!0,is}const xf=(...e)=>{const t=ra().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=oa(s);if(r)return n(r,!0,ia(r))},t};function ia(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function oa(e){return le(e)?document.querySelector(e):e}const la=window.__VP_SITE_DATA__;function fo(e){return Zr()?(ko(e),!0):!1}const os=new WeakMap,ca=(...e)=>{var t;const n=e[0],s=(t=en())==null?void 0:t.proxy;if(s==null&&!Di())throw new Error("injectLocal must be called in setup");return s&&os.has(s)&&n in os.get(s)?os.get(s)[n]:_t(...e)},uo=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const aa=Object.prototype.toString,fa=e=>aa.call(e)==="[object Object]",St=()=>{},Dr=ua();function ua(){var e,t;return uo&&((e=window==null?void 0:window.navigator)==null?void 0:e.userAgent)&&(/iP(?:ad|hone|od)/.test(window.navigator.userAgent)||((t=window==null?void 0:window.navigator)==null?void 0:t.maxTouchPoints)>2&&/iPad|Macintosh/.test(window==null?void 0:window.navigator.userAgent))}function Xs(e,t){function n(...s){return new Promise((r,i)=>{Promise.resolve(e(()=>t.apply(this,s),{fn:t,thisArg:this,args:s})).then(r).catch(i)})}return n}const ho=e=>e();function da(e,t={}){let n,s,r=St;const i=c=>{clearTimeout(c),r(),r=St};let o;return c=>{const u=ce(e),a=ce(t.maxWait);return n&&i(n),u<=0||a!==void 0&&a<=0?(s&&(i(s),s=null),Promise.resolve(c())):new Promise((h,v)=>{r=t.rejectOnCancel?v:h,o=c,a&&!s&&(s=setTimeout(()=>{n&&i(n),s=null,h(o())},a)),n=setTimeout(()=>{s&&i(s),s=null,h(c())},u)})}}function ha(...e){let t=0,n,s=!0,r=St,i,o,l,c,u;!ae(e[0])&&typeof e[0]=="object"?{delay:o,trailing:l=!0,leading:c=!0,rejectOnCancel:u=!1}=e[0]:[o,l=!0,c=!0,u=!1]=e;const a=()=>{n&&(clearTimeout(n),n=void 0,r(),r=St)};return v=>{const y=ce(o),A=Date.now()-t,P=()=>i=v();return a(),y<=0?(t=Date.now(),P()):(A>y&&(c||!s)?(t=Date.now(),P()):l&&(i=new Promise((K,H)=>{r=u?H:K,n=setTimeout(()=>{t=Date.now(),s=!0,K(P()),a()},Math.max(0,y-A))})),!c&&!n&&(n=setTimeout(()=>s=!0,y)),s=!1,i)}}function pa(e=ho,t={}){const{initialState:n="active"}=t,s=Ys(n==="active");function r(){s.value=!1}function i(){s.value=!0}const o=(...l)=>{s.value&&e(...l)};return{isActive:Fn(s),pause:r,resume:i,eventFilter:o}}function $r(e){return e.endsWith("rem")?Number.parseFloat(e)*16:Number.parseFloat(e)}function ga(e){return en()}function ls(e){return Array.isArray(e)?e:[e]}function Ys(...e){if(e.length!==1)return gl(...e);const t=e[0];return typeof t=="function"?Fn(dl(()=>({get:t,set:St}))):mt(t)}function ma(e,t=200,n={}){return Xs(da(t,n),e)}function va(e,t=200,n=!1,s=!0,r=!1){return Xs(ha(t,n,s,r),e)}function ya(e,t,n={}){const{eventFilter:s=ho,...r}=n;return Le(e,Xs(s,t),r)}function _a(e,t,n={}){const{eventFilter:s,initialState:r="active",...i}=n,{eventFilter:o,pause:l,resume:c,isActive:u}=pa(s,{initialState:r});return{stop:ya(e,t,{...i,eventFilter:o}),pause:l,resume:c,isActive:u}}function Un(e,t=!0,n){ga()?Lt(e,n):t?e():Dn(e)}function ba(e,t,n){return Le(e,t,{...n,immediate:!0})}const Ye=uo?window:void 0;function zs(e){var t;const n=ce(e);return(t=n==null?void 0:n.$el)!=null?t:n}function ze(...e){const t=[],n=()=>{t.forEach(l=>l()),t.length=0},s=(l,c,u,a)=>(l.addEventListener(c,u,a),()=>l.removeEventListener(c,u,a)),r=ie(()=>{const l=ls(ce(e[0])).filter(c=>c!=null);return l.every(c=>typeof c!="string")?l:void 0}),i=ba(()=>{var l,c;return[(c=(l=r.value)==null?void 0:l.map(u=>zs(u)))!=null?c:[Ye].filter(u=>u!=null),ls(ce(r.value?e[1]:e[0])),ls(ks(r.value?e[2]:e[1])),ce(r.value?e[3]:e[2])]},([l,c,u,a])=>{if(n(),!(l!=null&&l.length)||!(c!=null&&c.length)||!(u!=null&&u.length))return;const h=fa(a)?{...a}:a;t.push(...l.flatMap(v=>c.flatMap(y=>u.map(A=>s(v,y,A,h)))))},{flush:"post"}),o=()=>{i(),n()};return fo(n),o}function wa(){const e=Pe(!1),t=en();return t&&Lt(()=>{e.value=!0},t),e}function Sa(e){const t=wa();return ie(()=>(t.value,!!e()))}function xa(e){return typeof e=="function"?e:typeof e=="string"?t=>t.key===e:Array.isArray(e)?t=>e.includes(t.key):()=>!0}function Tf(...e){let t,n,s={};e.length===3?(t=e[0],n=e[1],s=e[2]):e.length===2?typeof e[1]=="object"?(t=!0,n=e[0],s=e[1]):(t=e[0],n=e[1]):(t=!0,n=e[0]);const{target:r=Ye,eventName:i="keydown",passive:o=!1,dedupe:l=!1}=s,c=xa(t);return ze(r,i,a=>{a.repeat&&ce(l)||c(a)&&n(a)},o)}const Ta=Symbol("vueuse-ssr-width");function Ea(){const e=Di()?ca(Ta,null):null;return typeof e=="number"?e:void 0}function po(e,t={}){const{window:n=Ye,ssrWidth:s=Ea()}=t,r=Sa(()=>n&&"matchMedia"in n&&typeof n.matchMedia=="function"),i=Pe(typeof s=="number"),o=Pe(),l=Pe(!1),c=u=>{l.value=u.matches};return Yi(()=>{if(i.value){i.value=!r.value;const u=ce(e).split(",");l.value=u.some(a=>{const h=a.includes("not all"),v=a.match(/\(\s*min-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/),y=a.match(/\(\s*max-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/);let A=!!(v||y);return v&&A&&(A=s>=$r(v[1])),y&&A&&(A=s<=$r(y[1])),h?!A:A});return}r.value&&(o.value=n.matchMedia(ce(e)),l.value=o.value.matches)}),ze(o,"change",c,{passive:!0}),ie(()=>l.value)}const dn=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},hn="__vueuse_ssr_handlers__",Ca=Aa();function Aa(){return hn in dn||(dn[hn]=dn[hn]||{}),dn[hn]}function go(e,t){return Ca[e]||t}function mo(e){return po("(prefers-color-scheme: dark)",e)}function Ra(e){return e==null?"any":e instanceof Set?"set":e instanceof Map?"map":e instanceof Date?"date":typeof e=="boolean"?"boolean":typeof e=="string"?"string":typeof e=="object"?"object":Number.isNaN(e)?"any":"number"}const Oa={boolean:{read:e=>e==="true",write:e=>String(e)},object:{read:e=>JSON.parse(e),write:e=>JSON.stringify(e)},number:{read:e=>Number.parseFloat(e),write:e=>String(e)},any:{read:e=>e,write:e=>String(e)},string:{read:e=>e,write:e=>String(e)},map:{read:e=>new Map(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e.entries()))},set:{read:e=>new Set(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e))},date:{read:e=>new Date(e),write:e=>e.toISOString()}},jr="vueuse-storage";function Ma(e,t,n,s={}){var r;const{flush:i="pre",deep:o=!0,listenToStorageChanges:l=!0,writeDefaults:c=!0,mergeDefaults:u=!1,shallow:a,window:h=Ye,eventFilter:v,onError:y=E=>{console.error(E)},initOnMounted:A}=s,P=(a?Pe:mt)(typeof t=="function"?t():t),K=ie(()=>ce(e));if(!n)try{n=go("getDefaultStorage",()=>{var E;return(E=Ye)==null?void 0:E.localStorage})()}catch(E){y(E)}if(!n)return P;const H=ce(t),k=Ra(H),p=(r=s.serializer)!=null?r:Oa[k],{pause:g,resume:M}=_a(P,()=>R(P.value),{flush:i,deep:o,eventFilter:v});Le(K,()=>T(),{flush:i}),h&&l&&Un(()=>{n instanceof Storage?ze(h,"storage",T,{passive:!0}):ze(h,jr,I),A&&T()}),A||T();function W(E,_){if(h){const N={key:K.value,oldValue:E,newValue:_,storageArea:n};h.dispatchEvent(n instanceof Storage?new StorageEvent("storage",N):new CustomEvent(jr,{detail:N}))}}function R(E){try{const _=n.getItem(K.value);if(E==null)W(_,null),n.removeItem(K.value);else{const N=p.write(E);_!==N&&(n.setItem(K.value,N),W(_,N))}}catch(_){y(_)}}function U(E){const _=E?E.newValue:n.getItem(K.value);if(_==null)return c&&H!=null&&n.setItem(K.value,p.write(H)),H;if(!E&&u){const N=p.read(_);return typeof u=="function"?u(N,H):k==="object"&&!Array.isArray(N)?{...H,...N}:N}else return typeof _!="string"?_:p.read(_)}function T(E){if(!(E&&E.storageArea!==n)){if(E&&E.key==null){P.value=H;return}if(!(E&&E.key!==K.value)){g();try{(E==null?void 0:E.newValue)!==p.write(P.value)&&(P.value=U(E))}catch(_){y(_)}finally{E?Dn(M):M()}}}}function I(E){T(E.detail)}return P}const Ia="*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}";function Pa(e={}){const{selector:t="html",attribute:n="class",initialValue:s="auto",window:r=Ye,storage:i,storageKey:o="vueuse-color-scheme",listenToStorageChanges:l=!0,storageRef:c,emitAuto:u,disableTransition:a=!0}=e,h={auto:"",light:"light",dark:"dark",...e.modes||{}},v=mo({window:r}),y=ie(()=>v.value?"dark":"light"),A=c||(o==null?Ys(s):Ma(o,s,i,{window:r,listenToStorageChanges:l})),P=ie(()=>A.value==="auto"?y.value:A.value),K=go("updateHTMLAttrs",(g,M,W)=>{const R=typeof g=="string"?r==null?void 0:r.document.querySelector(g):zs(g);if(!R)return;const U=new Set,T=new Set;let I=null;if(M==="class"){const _=W.split(/\s/g);Object.values(h).flatMap(N=>(N||"").split(/\s/g)).filter(Boolean).forEach(N=>{_.includes(N)?U.add(N):T.add(N)})}else I={key:M,value:W};if(U.size===0&&T.size===0&&I===null)return;let E;a&&(E=r.document.createElement("style"),E.appendChild(document.createTextNode(Ia)),r.document.head.appendChild(E));for(const _ of U)R.classList.add(_);for(const _ of T)R.classList.remove(_);I&&R.setAttribute(I.key,I.value),a&&(r.getComputedStyle(E).opacity,document.head.removeChild(E))});function H(g){var M;K(t,n,(M=h[g])!=null?M:g)}function k(g){e.onChanged?e.onChanged(g,H):H(g)}Le(P,k,{flush:"post",immediate:!0}),Un(()=>k(P.value));const p=ie({get(){return u?A.value:P.value},set(g){A.value=g}});return Object.assign(p,{store:A,system:y,state:P})}function La(e={}){const{valueDark:t="dark",valueLight:n=""}=e,s=Pa({...e,onChanged:(o,l)=>{var c;e.onChanged?(c=e.onChanged)==null||c.call(e,o==="dark",l,o):l(o)},modes:{dark:t,light:n}}),r=ie(()=>s.system.value);return ie({get(){return s.value==="dark"},set(o){const l=o?"dark":"light";r.value===l?s.value="auto":s.value=l}})}function cs(e){return typeof Window<"u"&&e instanceof Window?e.document.documentElement:typeof Document<"u"&&e instanceof Document?e.documentElement:e}const Vr=1;function Na(e,t={}){const{throttle:n=0,idle:s=200,onStop:r=St,onScroll:i=St,offset:o={left:0,right:0,top:0,bottom:0},eventListenerOptions:l={capture:!1,passive:!0},behavior:c="auto",window:u=Ye,onError:a=R=>{console.error(R)}}=t,h=Pe(0),v=Pe(0),y=ie({get(){return h.value},set(R){P(R,void 0)}}),A=ie({get(){return v.value},set(R){P(void 0,R)}});function P(R,U){var T,I,E,_;if(!u)return;const N=ce(e);if(!N)return;(E=N instanceof Document?u.document.body:N)==null||E.scrollTo({top:(T=ce(U))!=null?T:A.value,left:(I=ce(R))!=null?I:y.value,behavior:ce(c)});const Y=((_=N==null?void 0:N.document)==null?void 0:_.documentElement)||(N==null?void 0:N.documentElement)||N;y!=null&&(h.value=Y.scrollLeft),A!=null&&(v.value=Y.scrollTop)}const K=Pe(!1),H=Pt({left:!0,right:!1,top:!0,bottom:!1}),k=Pt({left:!1,right:!1,top:!1,bottom:!1}),p=R=>{K.value&&(K.value=!1,k.left=!1,k.right=!1,k.top=!1,k.bottom=!1,r(R))},g=ma(p,n+s),M=R=>{var U;if(!u)return;const T=((U=R==null?void 0:R.document)==null?void 0:U.documentElement)||(R==null?void 0:R.documentElement)||zs(R),{display:I,flexDirection:E,direction:_}=getComputedStyle(T),N=_==="rtl"?-1:1,Y=T.scrollLeft;k.left=Y<h.value,k.right=Y>h.value;const re=Math.abs(Y*N)<=(o.left||0),j=Math.abs(Y*N)+T.clientWidth>=T.scrollWidth-(o.right||0)-Vr;I==="flex"&&E==="row-reverse"?(H.left=j,H.right=re):(H.left=re,H.right=j),h.value=Y;let X=T.scrollTop;R===u.document&&!X&&(X=u.document.body.scrollTop),k.top=X<v.value,k.bottom=X>v.value;const D=Math.abs(X)<=(o.top||0),oe=Math.abs(X)+T.clientHeight>=T.scrollHeight-(o.bottom||0)-Vr;I==="flex"&&E==="column-reverse"?(H.top=oe,H.bottom=D):(H.top=D,H.bottom=oe),v.value=X},W=R=>{var U;if(!u)return;const T=(U=R.target.documentElement)!=null?U:R.target;M(T),K.value=!0,g(R),i(R)};return ze(e,"scroll",n?va(W,n,!0,!1):W,l),Un(()=>{try{const R=ce(e);if(!R)return;M(R)}catch(R){a(R)}}),ze(e,"scrollend",p,l),{x:y,y:A,isScrolling:K,arrivedState:H,directions:k,measure(){const R=ce(e);u&&R&&M(R)}}}function vo(e){const t=window.getComputedStyle(e);if(t.overflowX==="scroll"||t.overflowY==="scroll"||t.overflowX==="auto"&&e.clientWidth<e.scrollWidth||t.overflowY==="auto"&&e.clientHeight<e.scrollHeight)return!0;{const n=e.parentNode;return!n||n.tagName==="BODY"?!1:vo(n)}}function Fa(e){const t=e||window.event,n=t.target;return vo(n)?!1:t.touches.length>1?!0:(t.preventDefault&&t.preventDefault(),!1)}const as=new WeakMap;function Ef(e,t=!1){const n=Pe(t);let s=null,r="";Le(Ys(e),l=>{const c=cs(ce(l));if(c){const u=c;if(as.get(u)||as.set(u,u.style.overflow),u.style.overflow!=="hidden"&&(r=u.style.overflow),u.style.overflow==="hidden")return n.value=!0;if(n.value)return u.style.overflow="hidden"}},{immediate:!0});const i=()=>{const l=cs(ce(e));!l||n.value||(Dr&&(s=ze(l,"touchmove",c=>{Fa(c)},{passive:!1})),l.style.overflow="hidden",n.value=!0)},o=()=>{const l=cs(ce(e));!l||!n.value||(Dr&&(s==null||s()),l.style.overflow=r,as.delete(l),n.value=!1)};return fo(o),ie({get(){return n.value},set(l){l?i():o()}})}function Cf(e={}){const{window:t=Ye,...n}=e;return Na(t,n)}function Af(e={}){const{window:t=Ye,initialWidth:n=Number.POSITIVE_INFINITY,initialHeight:s=Number.POSITIVE_INFINITY,listenOrientation:r=!0,includeScrollbar:i=!0,type:o="inner"}=e,l=Pe(n),c=Pe(s),u=()=>{if(t)if(o==="outer")l.value=t.outerWidth,c.value=t.outerHeight;else if(o==="visual"&&t.visualViewport){const{width:h,height:v,scale:y}=t.visualViewport;l.value=Math.round(h*y),c.value=Math.round(v*y)}else i?(l.value=t.innerWidth,c.value=t.innerHeight):(l.value=t.document.documentElement.clientWidth,c.value=t.document.documentElement.clientHeight)};u(),Un(u);const a={passive:!0};if(ze("resize",u,a),t&&o==="visual"&&t.visualViewport&&ze(t.visualViewport,"resize",u,a),r){const h=po("(orientation: portrait)");Le(h,()=>u())}return{width:l,height:c}}const fs={};var us={};const yo=/^(?:[a-z]+:|\/\/)/i,Ha="vitepress-theme-appearance",Da=/#.*$/,$a=/[?#].*$/,ja=/(?:(^|\/)index)?\.(?:md|html)$/,ye=typeof document<"u",_o={relativePath:"404.md",filePath:"",title:"404",description:"Not Found",headers:[],frontmatter:{sidebar:!1,layout:"page"},lastUpdated:0,isNotFound:!0};function Va(e,t,n=!1){if(t===void 0)return!1;if(e=Wr(`/${e}`),n)return new RegExp(t).test(e);if(Wr(t)!==e)return!1;const s=t.match(Da);return s?(ye?location.hash:"")===s[0]:!0}function Wr(e){return decodeURI(e).replace($a,"").replace(ja,"$1")}function Wa(e){return yo.test(e)}function ka(e,t){return Object.keys((e==null?void 0:e.locales)||{}).find(n=>n!=="root"&&!Wa(n)&&Va(t,`/${n}/`,!0))||"root"}function Ua(e,t){var s,r,i,o,l,c,u;const n=ka(e,t);return Object.assign({},e,{localeIndex:n,lang:((s=e.locales[n])==null?void 0:s.lang)??e.lang,dir:((r=e.locales[n])==null?void 0:r.dir)??e.dir,title:((i=e.locales[n])==null?void 0:i.title)??e.title,titleTemplate:((o=e.locales[n])==null?void 0:o.titleTemplate)??e.titleTemplate,description:((l=e.locales[n])==null?void 0:l.description)??e.description,head:wo(e.head,((c=e.locales[n])==null?void 0:c.head)??[]),themeConfig:{...e.themeConfig,...(u=e.locales[n])==null?void 0:u.themeConfig}})}function bo(e,t){const n=t.title||e.title,s=t.titleTemplate??e.titleTemplate;if(typeof s=="string"&&s.includes(":title"))return s.replace(/:title/g,n);const r=Ba(e.title,s);return n===r.slice(3)?n:`${n}${r}`}function Ba(e,t){return t===!1?"":t===!0||t===void 0?` | ${e}`:e===t?"":` | ${t}`}function Ka(e,t){const[n,s]=t;if(n!=="meta")return!1;const r=Object.entries(s)[0];return r==null?!1:e.some(([i,o])=>i===n&&o[r[0]]===r[1])}function wo(e,t){return[...e.filter(n=>!Ka(t,n)),...t]}const qa=/[\u0000-\u001F"#$&*+,:;<=>?[\]^`{|}\u007F]/g,Ga=/^[a-z]:/i;function kr(e){const t=Ga.exec(e),n=t?t[0]:"";return n+e.slice(n.length).replace(qa,"_").replace(/(^|\/)_+(?=[^/]*$)/,"$1")}const ds=new Set;function Xa(e){if(ds.size===0){const n=typeof process=="object"&&(us==null?void 0:us.VITE_EXTRA_EXTENSIONS)||(fs==null?void 0:fs.VITE_EXTRA_EXTENSIONS)||"";("3g2,3gp,aac,ai,apng,au,avif,bin,bmp,cer,class,conf,crl,css,csv,dll,doc,eps,epub,exe,gif,gz,ics,ief,jar,jpe,jpeg,jpg,js,json,jsonld,m4a,man,mid,midi,mjs,mov,mp2,mp3,mp4,mpe,mpeg,mpg,mpp,oga,ogg,ogv,ogx,opus,otf,p10,p7c,p7m,p7s,pdf,png,ps,qt,roff,rtf,rtx,ser,svg,t,tif,tiff,tr,ts,tsv,ttf,txt,vtt,wav,weba,webm,webp,woff,woff2,xhtml,xml,yaml,yml,zip"+(n&&typeof n=="string"?","+n:"")).split(",").forEach(s=>ds.add(s))}const t=e.split(".").pop();return t==null||!ds.has(t.toLowerCase())}const Ya=Symbol(),wt=Pe(la);function Rf(e){const t=ie(()=>Ua(wt.value,e.data.relativePath)),n=t.value.appearance,s=n==="force-dark"?mt(!0):n==="force-auto"?mo():n?La({storageKey:Ha,initialValue:()=>n==="dark"?"dark":"auto",...typeof n=="object"?n:{}}):mt(!1),r=mt(ye?location.hash:"");return ye&&window.addEventListener("hashchange",()=>{r.value=location.hash}),Le(()=>e.data,()=>{r.value=ye?location.hash:""}),{site:t,theme:ie(()=>t.value.themeConfig),page:ie(()=>e.data),frontmatter:ie(()=>e.data.frontmatter),params:ie(()=>e.data.params),lang:ie(()=>t.value.lang),dir:ie(()=>e.data.frontmatter.dir||t.value.dir),localeIndex:ie(()=>t.value.localeIndex||"root"),title:ie(()=>bo(t.value,e.data)),description:ie(()=>e.data.description||t.value.description),isDark:s,hash:ie(()=>r.value)}}function za(){const e=_t(Ya);if(!e)throw new Error("vitepress data not properly injected in app");return e}function Ja(e,t){return`${e}${t}`.replace(/\/+/g,"/")}function Ur(e){return yo.test(e)||!e.startsWith("/")?e:Ja(wt.value.base,e)}function Qa(e){let t=e.replace(/\.html$/,"");if(t=decodeURIComponent(t),t=t.replace(/\/$/,"/index"),ye){const n="/docs/";t=kr(t.slice(n.length).replace(/\//g,"_")||"index")+".md";let s=__VP_HASH_MAP__[t.toLowerCase()];if(s||(t=t.endsWith("_index.md")?t.slice(0,-9)+".md":t.slice(0,-3)+"_index.md",s=__VP_HASH_MAP__[t.toLowerCase()]),!s)return null;t=`${n}assets/${t}.${s}.js`}else t=`./${kr(t.slice(1).replace(/\//g,"_"))}.md.js`;return t}let bn=[];function Of(e){bn.push(e),Vn(()=>{bn=bn.filter(t=>t!==e)})}function Za(){let e=wt.value.scrollOffset,t=0,n=24;if(typeof e=="object"&&"padding"in e&&(n=e.padding,e=e.selector),typeof e=="number")t=e;else if(typeof e=="string")t=Br(e,n);else if(Array.isArray(e))for(const s of e){const r=Br(s,n);if(r){t=r;break}}return t}function Br(e,t){const n=document.querySelector(e);if(!n)return 0;const s=n.getBoundingClientRect().bottom;return s<0?0:s+t}const ef=Symbol(),So="http://a.com",tf=()=>({path:"/",component:null,data:_o});function Mf(e,t){const n=Pt(tf()),s={route:n,go:r};async function r(l=ye?location.href:"/"){var c,u;l=hs(l),await((c=s.onBeforeRouteChange)==null?void 0:c.call(s,l))!==!1&&(ye&&l!==hs(location.href)&&(history.replaceState({scrollPosition:window.scrollY},""),history.pushState({},"",l)),await o(l),await((u=s.onAfterRouteChange??s.onAfterRouteChanged)==null?void 0:u(l)))}let i=null;async function o(l,c=0,u=!1){var v,y;if(await((v=s.onBeforePageLoad)==null?void 0:v.call(s,l))===!1)return;const a=new URL(l,So),h=i=a.pathname;try{let A=await e(h);if(!A)throw new Error(`Page not found: ${h}`);if(i===h){i=null;const{default:P,__pageData:K}=A;if(!P)throw new Error(`Invalid route component: ${P}`);await((y=s.onAfterPageLoad)==null?void 0:y.call(s,l)),n.path=ye?h:Ur(h),n.component=vn(P),n.data=vn(K),ye&&Dn(()=>{let H=wt.value.base+K.relativePath.replace(/(?:(^|\/)index)?\.md$/,"$1");if(!wt.value.cleanUrls&&!H.endsWith("/")&&(H+=".html"),H!==a.pathname&&(a.pathname=H,l=H+a.search+a.hash,history.replaceState({},"",l)),a.hash&&!c){let k=null;try{k=document.getElementById(decodeURIComponent(a.hash).slice(1))}catch(p){console.warn(p)}if(k){Kr(k,a.hash);return}}window.scrollTo(0,c)})}}catch(A){if(!/fetch|Page not found/.test(A.message)&&!/^\/404(\.html|\/)?$/.test(l)&&console.error(A),!u)try{const P=await fetch(wt.value.base+"hashmap.json");window.__VP_HASH_MAP__=await P.json(),await o(l,c,!0);return}catch{}if(i===h){i=null,n.path=ye?h:Ur(h),n.component=t?vn(t):null;const P=ye?h.replace(/(^|\/)$/,"$1index").replace(/(\.html)?$/,".md").replace(/^\//,""):"404.md";n.data={..._o,relativePath:P}}}}return ye&&(history.state===null&&history.replaceState({},""),window.addEventListener("click",l=>{if(l.defaultPrevented||!(l.target instanceof Element)||l.target.closest("button")||l.button!==0||l.ctrlKey||l.shiftKey||l.altKey||l.metaKey)return;const c=l.target.closest("a");if(!c||c.closest(".vp-raw")||c.hasAttribute("download")||c.hasAttribute("target"))return;const u=c.getAttribute("href")??(c instanceof SVGAElement?c.getAttribute("xlink:href"):null);if(u==null)return;const{href:a,origin:h,pathname:v,hash:y,search:A}=new URL(u,c.baseURI),P=new URL(location.href);h===P.origin&&Xa(v)&&(l.preventDefault(),v===P.pathname&&A===P.search?(y!==P.hash&&(history.pushState({},"",a),window.dispatchEvent(new HashChangeEvent("hashchange",{oldURL:P.href,newURL:a}))),y?Kr(c,y,c.classList.contains("header-anchor")):window.scrollTo(0,0)):r(a))},{capture:!0}),window.addEventListener("popstate",async l=>{var u;if(l.state===null)return;const c=hs(location.href);await o(c,l.state&&l.state.scrollPosition||0),await((u=s.onAfterRouteChange??s.onAfterRouteChanged)==null?void 0:u(c))}),window.addEventListener("hashchange",l=>{l.preventDefault()})),s}function nf(){const e=_t(ef);if(!e)throw new Error("useRouter() is called without provider.");return e}function xo(){return nf().route}function Kr(e,t,n=!1){let s=null;try{s=e.classList.contains("header-anchor")?e:document.getElementById(decodeURIComponent(t).slice(1))}catch(r){console.warn(r)}if(s){let r=function(){!n||Math.abs(o-window.scrollY)>window.innerHeight?window.scrollTo(0,o):window.scrollTo({left:0,top:o,behavior:"smooth"})};const i=parseInt(window.getComputedStyle(s).paddingTop,10),o=window.scrollY+s.getBoundingClientRect().top-Za()+i;requestAnimationFrame(r)}}function hs(e){const t=new URL(e,So);return t.pathname=t.pathname.replace(/(^|\/)index(\.html)?$/,"$1"),wt.value.cleanUrls?t.pathname=t.pathname.replace(/\.html$/,""):!t.pathname.endsWith("/")&&!t.pathname.endsWith(".html")&&(t.pathname+=".html"),t.pathname+t.search+t.hash}const pn=()=>bn.forEach(e=>e()),If=Ci({name:"VitePressContent",props:{as:{type:[Object,String],default:"div"}},setup(e){const t=xo(),{frontmatter:n,site:s}=za();return Le(n,pn,{deep:!0,flush:"post"}),()=>Rs(e.as,s.value.contentProps??{style:{position:"relative"}},[t.component?Rs(t.component,{onVnodeMounted:pn,onVnodeUpdated:pn,onVnodeUnmounted:pn}):"404 Page Not Found"])}}),Pf=(e,t)=>{const n=e.__vccOpts||e;for(const[s,r]of t)n[s]=r;return n},Lf=Ci({setup(e,{slots:t}){const n=mt(!1);return Lt(()=>{n.value=!0}),()=>n.value&&t.default?t.default():null}});function Nf(){ye&&window.addEventListener("click",e=>{var n;const t=e.target;if(t.matches(".vp-code-group input")){const s=(n=t.parentElement)==null?void 0:n.parentElement;if(!s)return;const r=Array.from(s.querySelectorAll("input")).indexOf(t);if(r<0)return;const i=s.querySelector(".blocks");if(!i)return;const o=Array.from(i.children).find(u=>u.classList.contains("active"));if(!o)return;const l=i.children[r];if(!l||o===l)return;o.classList.remove("active"),l.classList.add("active");const c=s==null?void 0:s.querySelector(`label[for="${t.id}"]`);c==null||c.scrollIntoView({block:"nearest"})}})}function Ff(){if(ye){const e=new WeakMap;window.addEventListener("click",t=>{var s;const n=t.target;if(n.matches('div[class*="language-"] > button.copy')){const r=n.parentElement,i=(s=n.nextElementSibling)==null?void 0:s.nextElementSibling;if(!r||!i)return;const o=/language-(shellscript|shell|bash|sh|zsh)/.test(r.className),l=[".vp-copy-ignore",".diff.remove"],c=i.cloneNode(!0);c.querySelectorAll(l.join(",")).forEach(a=>a.remove());let u=c.textContent||"";o&&(u=u.replace(/^ *(\$|>) /gm,"").trim()),sf(u).then(()=>{n.classList.add("copied"),clearTimeout(e.get(n));const a=setTimeout(()=>{n.classList.remove("copied"),n.blur(),e.delete(n)},2e3);e.set(n,a)})}})}}async function sf(e){try{return navigator.clipboard.writeText(e)}catch{const t=document.createElement("textarea"),n=document.activeElement;t.value=e,t.setAttribute("readonly",""),t.style.contain="strict",t.style.position="absolute",t.style.left="-9999px",t.style.fontSize="12pt";const s=document.getSelection(),r=s?s.rangeCount>0&&s.getRangeAt(0):null;document.body.appendChild(t),t.select(),t.selectionStart=0,t.selectionEnd=e.length,document.execCommand("copy"),document.body.removeChild(t),r&&(s.removeAllRanges(),s.addRange(r)),n&&n.focus()}}function Hf(e,t){let n=!0,s=[];const r=i=>{if(n){n=!1,i.forEach(l=>{const c=ps(l);for(const u of document.head.children)if(u.isEqualNode(c)){s.push(u);return}});return}const o=i.map(ps);s.forEach((l,c)=>{const u=o.findIndex(a=>a==null?void 0:a.isEqualNode(l??null));u!==-1?delete o[u]:(l==null||l.remove(),delete s[c])}),o.forEach(l=>l&&document.head.appendChild(l)),s=[...s,...o].filter(Boolean)};Yi(()=>{const i=e.data,o=t.value,l=i&&i.description,c=i&&i.frontmatter.head||[],u=bo(o,i);u!==document.title&&(document.title=u);const a=l||o.description;let h=document.querySelector("meta[name=description]");h?h.getAttribute("content")!==a&&h.setAttribute("content",a):ps(["meta",{name:"description",content:a}]),r(wo(o.head,of(c)))})}function ps([e,t,n]){const s=document.createElement(e);for(const r in t)s.setAttribute(r,t[r]);return n&&(s.innerHTML=n),e==="script"&&t.async==null&&(s.async=!1),s}function rf(e){return e[0]==="meta"&&e[1]&&e[1].name==="description"}function of(e){return e.filter(t=>!rf(t))}const gs=new Set,To=()=>document.createElement("link"),lf=e=>{const t=To();t.rel="prefetch",t.href=e,document.head.appendChild(t)},cf=e=>{const t=new XMLHttpRequest;t.open("GET",e,t.withCredentials=!0),t.send()};let gn;const af=ye&&(gn=To())&&gn.relList&&gn.relList.supports&&gn.relList.supports("prefetch")?lf:cf;function Df(){if(!ye||!window.IntersectionObserver)return;let e;if((e=navigator.connection)&&(e.saveData||/2g/.test(e.effectiveType)))return;const t=window.requestIdleCallback||setTimeout;let n=null;const s=()=>{n&&n.disconnect(),n=new IntersectionObserver(i=>{i.forEach(o=>{if(o.isIntersecting){const l=o.target;n.unobserve(l);const{pathname:c}=l;if(!gs.has(c)){gs.add(c);const u=Qa(c);u&&af(u)}}})}),t(()=>{document.querySelectorAll("#app a").forEach(i=>{const{hostname:o,pathname:l}=new URL(i.href instanceof SVGAnimatedString?i.href.animVal:i.href,i.baseURI),c=l.match(/\.\w+$/);c&&c[0]!==".html"||i.target!=="_blank"&&o===location.hostname&&(l!==location.pathname?n.observe(i):gs.add(l))})})};Lt(s);const r=xo();Le(()=>r.path,s),Vn(()=>{n&&n.disconnect()})}export{gf as $,Za as A,df as B,ff as C,Pe as D,Of as E,Se as F,ge as G,uf as H,yo as I,xo as J,_c as K,_t as L,Af as M,Ns as N,Tf as O,Dn as P,Cf as Q,ye as R,Fn as S,bf as T,Ef as U,Ql as V,pf as W,Sf as X,Oi as Y,wf as Z,Pf as _,ro as a,Rs as a0,Hf as a1,ef as a2,Rf as a3,Ya as a4,If as a5,Lf as a6,wt as a7,Mf as a8,Qa as a9,xf as aa,Df as ab,Ff as ac,Nf as ad,yf as ae,Cs as b,vf as c,Ci as d,_f as e,Xa as f,Ur as g,ie as h,Wa as i,so as j,ks as k,Va as l,po as m,Fs as n,Es as o,mt as p,Le as q,hf as r,Yi as s,Vo as t,za as u,Lt as v,Tl as w,Vn as x,mf as y,$l as z};
