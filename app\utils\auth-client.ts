import { createAuthClient } from "better-auth/vue"
import { inferAdditionalFields } from "better-auth/client/plugins";
import type { auth } from "@@/auth";

export const authClient = createAuthClient({
  /** The base URL of the server (optional if you're using the same domain) */
  // baseURL: process.env.NODE_ENV === 'development' ? `http://localhost:${process.env.PORT ?? 3000}` : 'https://gomark.pro',
  plugins: [inferAdditionalFields<typeof auth>()],
})
