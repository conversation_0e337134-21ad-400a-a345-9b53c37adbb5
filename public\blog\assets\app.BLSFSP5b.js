import{R as p}from"./chunks/theme.C-9tmA-F.js";import{l as s,m as i,R as u,n as c,p as l,C as f,q as d,s as m,v as h,w as A,x as g,d as v,u as w,y,z as C,A as R,B as P,D as E,E as b}from"./chunks/framework._wHCgU2P.js";function r(e){if(e.extends){const t=r(e.extends);return{...t,...e,async enhanceApp(a){t.enhanceApp&&await t.enhanceApp(a),e.enhanceApp&&await e.enhanceApp(a)}}}return e}const n=r(p),S=v({name:"VitePressApp",setup(){const{site:e,lang:t,dir:a}=w();return y(()=>{C(()=>{document.documentElement.lang=t.value,document.documentElement.dir=a.value})}),e.value.router.prefetchLinks&&R(),<PERSON>(),<PERSON>(),n.setup&&n.setup(),()=>b(n.Layout)}});async function T(){globalThis.__VITEPRESS__=!0;const e=x(),t=D();t.provide(u,e);const a=c(e.route);return t.provide(l,a),t.component("Content",f),t.component("ClientOnly",d),Object.defineProperties(t.config.globalProperties,{$frontmatter:{get(){return a.frontmatter.value}},$params:{get(){return a.page.value.params}}}),n.enhanceApp&&await n.enhanceApp({app:t,router:e,siteData:m}),{app:t,router:e,data:a}}function D(){return g(S)}function x(){let e=s;return h(t=>{let a=A(t),o=null;return a&&(e&&(a=a.replace(/\.js$/,".lean.js")),o=import(a)),s&&(e=!1),o},n.NotFound)}s&&T().then(({app:e,router:t,data:a})=>{t.go().then(()=>{i(t.route,a.site),e.mount("#app")})});export{T as createApp};
