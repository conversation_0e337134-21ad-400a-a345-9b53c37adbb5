import{_ as t,c as a,o,k as r}from"./chunks/framework._wHCgU2P.js";const c=JSON.parse('{"title":"Reflections for 2020-2021","description":"","frontmatter":{"title":"Reflections for 2020-2021","date":"2021-01-11T00:00:00.000Z","author":"Evan You","gravatar":"eca93da2c67aadafe35d477aa8f454b8","twitter":"@youyuxi"},"headers":[],"relativePath":"posts/hello-2021.md","filePath":"posts/hello-2021.md"}'),i={name:"posts/hello-2021.md"};function s(n,e,l,h,u,p){return o(),a("div",null,e[0]||(e[0]=[r("",13)]))}const f=t(i,[["render",s]]);export{c as __pageData,f as default};
