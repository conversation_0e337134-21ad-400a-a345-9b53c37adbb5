<script setup lang="ts">
import { Toaster } from '@/components/ui/sonner'

const route = useRoute()
const appConfig = useAppConfig()

const { init: initAnalytics } = useAnalytics()
initAnalytics()

useSeoMeta({
  title: computed(() => route.meta.title ? `${appConfig.appName} - ${route.meta.title}` : `${appConfig.appName}`).value as string,
  description: computed(() => route.meta.description ? route.meta.description : `${appConfig.appDescription}`).value as string,
  ogTitle: computed(() => route.meta.title ? `${appConfig.appName} - ${route.meta.title}` : `${appConfig.appName}`).value as string,
  ogDescription: computed(() => route.meta.description ? route.meta.description : `${appConfig.appDescription}`).value as string,
})
</script>

<template>
  <TooltipProvider :delay-duration="0">
    <NuxtLoadingIndicator
      style="opacity: 1;"
      :height="2"
    />
    <Toaster
      position="top-center"
      :toastOptions="{
        classes: {
          error: '!bg-destructive',
          success: '',
          warning: '',
          info: '',
          toast: '!bg-background !text-foreground',
          title: 'font-semibold',
          description: '',
        },
      }"
    />
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
  </TooltipProvider>
</template>
