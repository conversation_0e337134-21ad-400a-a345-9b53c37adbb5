<script setup lang="ts">
const { brand, contactEmail } = useRuntimeConfig().public

const footerData = [{
  title: 'Product',
  links: [{
    text: 'Features',
    url: '/#features',
  },
  {
    text: 'Testimonials',
    url: '/#testimonials',
  },
  {
    text: 'Pricing',
    url: '/#pricing',
  },
  ],
},
{
  title: 'Support',
  links: [{
    text: 'Documentation',
    url: '/docs',
  },
  {
    text: 'Twiiter',
    url: '#',
  },
  {
    text: 'Discord',
    url: '#',
  },
  ],
},
{
  title: 'Company',
  links: [{
    text: 'Blog',
    url: '/blog',
  },
  {
    text: 'Email Us',
    url: `mailto:${contactEmail}`,
  },
  ],
},
{
  title: 'Legal',
  links: [{
    text: 'Terms of Service',
    url: '/legal/terms-of-services',
  },
  {
    text: 'Privacy Policy',
    url: '/legal/privacy-policy',
  },
  {
    text: 'License',
    url: '/legal/license',
  },
  ],
},
]
</script>

<template>
  <footer class="container flex flex-col gap-12 py-24 md:flex-row md:gap-8">
    <div class="flex min-w-48 flex-col gap-4 md:min-w-72">
      <NuxtLink
        class="text-xl font-semibold tracking-tight"
        :aria-label="brand"
        href="/"
      >
        {{ brand }}
      </NuxtLink>
      <div class="flex gap-4 text-muted-foreground">
        <a
          class="outline-none transition duration-150 ease-in-out"
          rel="noreferrer"
          target="_blank"
          href="#"
        >
          <Icon
            name="ri:twitter-x-fill"
            class="size-6"
          />
        </a>
        <a
          class="outline-none transition duration-150 ease-in-out"
          rel="noreferrer"
          target="_blank"
          href="#"
        >
          <Icon
            name="ri:github-fill"
            class="size-6"
          />
        </a>
        <a
          class="outline-none transition duration-150 ease-in-out"
          rel="noreferrer"
          target="_blank"
          href="#"
        >
          <Icon
            name="ri:discord-fill"
            class="size-6"
          />
        </a>
        <a
          class="outline-none transition duration-150 ease-in-out"
          rel="noreferrer"
          target="_blank"
          href="#"
        >
          <Icon
            name="ri:linkedin-box-fill"
            class="size-6"
          />
        </a>
      </div>
    </div>
    <div class="grid w-full grid-cols-2 gap-8 lg:grid-cols-4">
      <div
        v-for="(menu, index) in footerData"
        :key="index"
        class="flex flex-col gap-4"
      >
        <p class="mb-2 ml-1 text-sm font-semibold">
          {{ menu.title }}
        </p>
        <ul class="flex flex-col gap-4">
          <li
            v-for="(link, linkIndex) in menu.links"
            :key="linkIndex"
          >
            <!-- 对于 /docs 和 /blog 路径使用 a 标签，因为它们是 VitePress 生成的静态文件 -->
            <a
              v-if="link.url === '/docs' || link.url === '/blog'"
              :href="link.url"
              class="px-1 py-0.5 text-sm text-muted-foreground hover:text-foreground"
            >
              {{ link.text }}
            </a>
            <!-- 其他路径继续使用 NuxtLink -->
            <NuxtLink
              v-else
              :href="link.url"
              class="px-1 py-0.5 text-sm text-muted-foreground hover:text-foreground"
            >
              {{ link.text }}
            </NuxtLink>
          </li>
        </ul>
      </div>
    </div>
  </footer>
</template>
