<!DOCTYPE html>
<html lang="en-US" dir="ltr">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title>The Vue Point</title>
    <meta name="description" content="The official blog for the Vue.js project">
    <meta name="generator" content="VitePress v1.6.4">
    <link rel="preload stylesheet" href="/blog/assets/style.BsMwNMTh.css" as="style">
    <link rel="preload stylesheet" href="/blog/vp-icons.css" as="style">
    
    <script type="module" src="/blog/assets/app.BLSFSP5b.js"></script>
    <link rel="modulepreload" href="/blog/assets/chunks/theme.C-9tmA-F.js">
    <link rel="modulepreload" href="/blog/assets/chunks/framework._wHCgU2P.js">
    <link rel="modulepreload" href="/blog/assets/index.md.DzYrHPtc.lean.js">
    <meta name="twitter:site" content="@vuejs">
    <meta name="twitter:card" content="summary">
    <meta name="twitter:image" content="https://vuejs.org/images/logo.png">
    <script id="check-dark-mode">(()=>{const e=localStorage.getItem("vitepress-theme-appearance")||"auto",a=window.matchMedia("(prefers-color-scheme: dark)").matches;(!e||e==="auto"?a:e==="dark")&&document.documentElement.classList.add("dark")})();</script>
    <script id="check-mac-os">document.documentElement.classList.toggle("mac",/Mac|iPhone|iPod|iPad/i.test(navigator.platform));</script>
  </head>
  <body>
    <div id="app"><div class="antialiased dark:bg-slate-900"><div class="max-w-3xl mx-auto px-4 sm:px-6 xl:max-w-5xl xl:px-0"><nav class="flex justify-between items-center py-10 font-bold"><a class="text-xl" href="/blog/" aria-label="The Vue Point"><img class="inline-block mr-2" style="width:36px;height:31px;" alt="logo" src="/logo.svg"><!----></a><div class="text-sm text-gray-500 dark:text-white leading-5"><a class="hover:text-gray-700 dark:hover:text-gray-200" href="https://github.com/vuejs/blog" target="_blank" rel="noopener"><span class="hidden sm:inline">GitHub </span>Source</a><span class="mr-2 ml-2">·</span><a class="hover:text-gray-700 dark:hover:text-gray-200 vp-raw" href="/feed.rss">RSS<span class="hidden sm:inline"> Feed</span></a><span class="mr-2 ml-2">·</span><a class="hover:text-gray-700 dark:hover:text-gray-200" href="https://vuejs.org" target="_blank" rel="noopener">Vuejs.org →</a></div></nav></div><main class="max-w-3xl mx-auto px-4 sm:px-6 xl:max-w-5xl xl:px-0"><div class="divide-y divide-gray-200 dark:divide-slate-200/5"><div class="pt-6 pb-8 space-y-2 md:space-y-5"><h1 class="text-3xl leading-9 font-extrabold text-gray-900 dark:text-white tracking-tight sm:text-4xl sm:leading-10 md:text-6xl md:leading-14">The Vue Point</h1><p class="text-lg leading-7 text-gray-500 dark:text-white">Updates, tips &amp; opinions from the maintainers of Vue.js.</p></div><ul class="divide-y divide-gray-200 dark:divide-slate-200/5"><!--[--><li class="py-12"><article class="space-y-2 xl:grid xl:grid-cols-4 xl:space-y-0 xl:items-baseline"><dl><dt class="sr-only">Published on</dt><dd class="text-base leading-6 font-medium text-gray-500 dark:text-gray-300"><time datetime="2025-08-07T12:00:00.000Z">August 7, 2025</time></dd></dl><div class="space-y-5 xl:col-span-3"><div class="space-y-6"><h2 class="text-2xl leading-8 font-bold tracking-tight"><a class="text-gray-900 dark:text-white" href="/blog/posts/how-to-use-images.html">如何在博客中使用图片</a></h2><!----></div><div class="text-base leading-6 font-medium"><a class="link" aria-label="read more" href="/blog/posts/how-to-use-images.html">Read more →</a></div></div></article></li><li class="py-12"><article class="space-y-2 xl:grid xl:grid-cols-4 xl:space-y-0 xl:items-baseline"><dl><dt class="sr-only">Published on</dt><dd class="text-base leading-6 font-medium text-gray-500 dark:text-gray-300"><time datetime="2023-01-01T12:00:00.000Z">January 1, 2023</time></dd></dl><div class="space-y-5 xl:col-span-3"><div class="space-y-6"><h2 class="text-2xl leading-8 font-bold tracking-tight"><a class="text-gray-900 dark:text-white" href="/blog/posts/2022-year-in-review.html">2022 Year In Review</a></h2><div class="prose dark:prose-invert max-w-none text-gray-500 dark:text-gray-300"><p>Happy new year, Vue community! With 2023 upon us, we would like to take this opportunity to recap what happened in 2022, and discuss what to expect in 2023.</p>
</div></div><div class="text-base leading-6 font-medium"><a class="link" aria-label="read more" href="/blog/posts/2022-year-in-review.html">Read more →</a></div></div></article></li><li class="py-12"><article class="space-y-2 xl:grid xl:grid-cols-4 xl:space-y-0 xl:items-baseline"><dl><dt class="sr-only">Published on</dt><dd class="text-base leading-6 font-medium text-gray-500 dark:text-gray-300"><time datetime="2021-01-11T12:00:00.000Z">January 11, 2021</time></dd></dl><div class="space-y-5 xl:col-span-3"><div class="space-y-6"><h2 class="text-2xl leading-8 font-bold tracking-tight"><a class="text-gray-900 dark:text-white" href="/blog/posts/hello-2021.html">Reflections for 2020-2021</a></h2><div class="prose dark:prose-invert max-w-none text-gray-500 dark:text-gray-300"><p>With a new year upon us, we also have a new blog! In this post, we would like to take a look back at some of the highlights from 2020, as well as some ideas we have for 2021.</p>
</div></div><div class="text-base leading-6 font-medium"><a class="link" aria-label="read more" href="/blog/posts/hello-2021.html">Read more →</a></div></div></article></li><!--]--></ul></div></main></div></div>
    <script>window.__VP_HASH_MAP__=JSON.parse("{\"index.md\":\"DzYrHPtc\",\"posts_2022-year-in-review.md\":\"CClB9OOf\",\"posts_hello-2021.md\":\"CWsHxIdM\",\"posts_how-to-use-images.md\":\"DriCg1x6\"}");window.__VP_SITE_DATA__=JSON.parse("{\"lang\":\"en-US\",\"dir\":\"ltr\",\"title\":\"The Vue Point\",\"description\":\"The official blog for the Vue.js project\",\"base\":\"/blog/\",\"head\":[],\"router\":{\"prefetchLinks\":true},\"appearance\":true,\"themeConfig\":{},\"locales\":{},\"scrollOffset\":134,\"cleanUrls\":false}");</script>
    
  </body>
</html>