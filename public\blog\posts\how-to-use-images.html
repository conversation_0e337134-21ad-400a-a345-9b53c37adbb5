<!DOCTYPE html>
<html lang="en-US" dir="ltr">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title>如何在博客中使用图片 | The Vue Point</title>
    <meta name="description" content="演示如何在 VitePress 博客文章中使用图片资源">
    <meta name="generator" content="VitePress v1.6.4">
    <link rel="preload stylesheet" href="/blog/assets/style.BsMwNMTh.css" as="style">
    <link rel="preload stylesheet" href="/blog/vp-icons.css" as="style">
    
    <script type="module" src="/blog/assets/app.BLSFSP5b.js"></script>
    <link rel="modulepreload" href="/blog/assets/chunks/theme.C-9tmA-F.js">
    <link rel="modulepreload" href="/blog/assets/chunks/framework._wHCgU2P.js">
    <link rel="modulepreload" href="/blog/assets/posts_how-to-use-images.md.DriCg1x6.lean.js">
    <meta name="twitter:site" content="@vuejs">
    <meta name="twitter:card" content="summary">
    <meta name="twitter:image" content="https://vuejs.org/images/logo.png">
    <script id="check-dark-mode">(()=>{const e=localStorage.getItem("vitepress-theme-appearance")||"auto",a=window.matchMedia("(prefers-color-scheme: dark)").matches;(!e||e==="auto"?a:e==="dark")&&document.documentElement.classList.add("dark")})();</script>
    <script id="check-mac-os">document.documentElement.classList.toggle("mac",/Mac|iPhone|iPod|iPad/i.test(navigator.platform));</script>
  </head>
  <body>
    <div id="app"><div class="antialiased dark:bg-slate-900"><div class="max-w-3xl mx-auto px-4 sm:px-6 xl:max-w-5xl xl:px-0"><nav class="flex justify-between items-center py-10 font-bold"><a class="text-xl" href="/blog/" aria-label="The Vue Point"><img class="inline-block mr-2" style="width:36px;height:31px;" alt="logo" src="/logo.svg"><span class="hidden md:inline dark:text-white">The Vue Point</span></a><div class="text-sm text-gray-500 dark:text-white leading-5"><a class="hover:text-gray-700 dark:hover:text-gray-200" href="https://github.com/vuejs/blog" target="_blank" rel="noopener"><span class="hidden sm:inline">GitHub </span>Source</a><span class="mr-2 ml-2">·</span><a class="hover:text-gray-700 dark:hover:text-gray-200 vp-raw" href="/feed.rss">RSS<span class="hidden sm:inline"> Feed</span></a><span class="mr-2 ml-2">·</span><a class="hover:text-gray-700 dark:hover:text-gray-200" href="https://vuejs.org" target="_blank" rel="noopener">Vuejs.org →</a></div></nav></div><main class="max-w-3xl mx-auto px-4 sm:px-6 xl:max-w-5xl xl:px-0"><article class="xl:divide-y xl:divide-gray-200 dark:xl:divide-slate-200/5"><header class="pt-6 xl:pb-10 space-y-1 text-center"><dl><dt class="sr-only">Published on</dt><dd class="text-base leading-6 font-medium text-gray-500 dark:text-gray-300"><time datetime="2025-08-07T12:00:00.000Z">August 7, 2025</time></dd></dl><h1 class="text-3xl leading-9 font-extrabold text-gray-900 dark:text-white tracking-tight sm:text-4xl sm:leading-10 md:text-5xl md:leading-14">如何在博客中使用图片</h1></header><div class="divide-y xl:divide-y-0 divide-gray-200 dark:divide-slate-200/5 xl:grid xl:grid-cols-4 xl:gap-x-10 pb-16 xl:pb-20" style="grid-template-rows:auto 1fr;"><dl class="pt-6 pb-10 xl:pt-11 xl:border-b xl:border-gray-200 dark:xl:border-slate-200/5"><dt class="sr-only">Authors</dt><dd><ul class="flex justify-center xl:block space-x-8 sm:space-x-12 xl:space-x-0 xl:space-y-8"><li class="flex items-center space-x-2"><!----><dl class="text-sm font-medium leading-5 whitespace-nowrap"><dt class="sr-only">Name</dt><dd class="text-gray-900 dark:text-white">{
  &quot;name&quot;: &quot;Blog Author&quot;,
  &quot;link&quot;: &quot;https://github.com/author&quot;
}</dd><!----><!----></dl></li></ul></dd></dl><div class="divide-y divide-gray-200 dark:divide-slate-200/5 xl:pb-0 xl:col-span-3 xl:row-span-2"><div style="position:relative;" class="prose dark:prose-invert max-w-none pt-10 pb-8"><div><h1 id="如何在博客中使用图片" tabindex="-1">如何在博客中使用图片 <a class="header-anchor" href="#如何在博客中使用图片" aria-label="Permalink to &quot;如何在博客中使用图片&quot;">​</a></h1><p>这篇文章演示了如何在 VitePress 博客中使用图片资源。</p><h2 id="使用-public-目录中的图片" tabindex="-1">使用 public 目录中的图片 <a class="header-anchor" href="#使用-public-目录中的图片" aria-label="Permalink to &quot;使用 public 目录中的图片&quot;">​</a></h2><p>VitePress 会自动处理 <code>blog/public</code> 目录下的静态资源。你可以直接引用这些文件：</p><h3 id="示例-引用-logo" tabindex="-1">示例：引用 logo <a class="header-anchor" href="#示例-引用-logo" aria-label="Permalink to &quot;示例：引用 logo&quot;">​</a></h3><div class="language-markdown vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">markdown</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">![</span><span style="--shiki-light:#032F62;--shiki-light-text-decoration:underline;--shiki-dark:#DBEDFF;--shiki-dark-text-decoration:underline;">Logo</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">](</span><span style="--shiki-light:#24292E;--shiki-light-text-decoration:underline;--shiki-dark:#E1E4E8;--shiki-dark-text-decoration:underline;">../public/images/pic.svg</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">)</span></span></code></pre></div><p>实际效果：</p><p><img src="data:image/svg+xml,%3csvg%20xmlns=&#39;http://www.w3.org/2000/svg&#39;%20xmlns:xlink=&#39;http://www.w3.org/1999/xlink&#39;%20fill=&#39;none&#39;%20version=&#39;1.1&#39;%20width=&#39;512&#39;%20height=&#39;512&#39;%20viewBox=&#39;0%200%20512%20512&#39;%3e%3cdefs%3e%3cclipPath%20id=&#39;master_svg0_50_3&#39;%3e%3crect%20x=&#39;0&#39;%20y=&#39;0&#39;%20width=&#39;512&#39;%20height=&#39;512&#39;%20rx=&#39;75&#39;/%3e%3c/clipPath%3e%3c/defs%3e%3cg%20clip-path=&#39;url(%23master_svg0_50_3)&#39;%3e%3crect%20x=&#39;0&#39;%20y=&#39;0&#39;%20width=&#39;512&#39;%20height=&#39;512&#39;%20rx=&#39;75&#39;%20fill=&#39;%23000000&#39;%20fill-opacity=&#39;1&#39;/%3e%3cg%3e%3cpath%20d=&#39;M223.68,391L289.03999999999996,391L289.03999999999996,305.88L363.52,144L295.12,144L276.88,195.68C270.42,214.3,263.96,231.4,257.5,250.78L255.98000000000002,250.78C249.51999999999998,231.4,243.44,214.3,237.36,195.68L219.12,144L149.2,144L223.68,305.88L223.68,391Z&#39;%20fill=&#39;%23D9D9D9&#39;%20fill-opacity=&#39;1&#39;/%3e%3c/g%3e%3c/g%3e%3c/svg%3e" alt="Logo"></p><h2 id="图片路径规则" tabindex="-1">图片路径规则 <a class="header-anchor" href="#图片路径规则" aria-label="Permalink to &quot;图片路径规则&quot;">​</a></h2><ol><li><p><strong>绝对路径</strong>：以 <code>/blog/</code> 开头，指向 <code>blog/public</code> 目录</p><div class="language-markdown vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">markdown</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">![</span><span style="--shiki-light:#032F62;--shiki-light-text-decoration:underline;--shiki-dark:#DBEDFF;--shiki-dark-text-decoration:underline;">图片描述</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">](</span><span style="--shiki-light:#24292E;--shiki-light-text-decoration:underline;--shiki-dark:#E1E4E8;--shiki-dark-text-decoration:underline;">/your-image.png</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">)</span></span></code></pre></div></li><li><p><strong>相对路径</strong>：相对于当前 markdown 文件</p><div class="language-markdown vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">markdown</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">![</span><span style="--shiki-light:#032F62;--shiki-light-text-decoration:underline;--shiki-dark:#DBEDFF;--shiki-dark-text-decoration:underline;">图片描述</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">](</span><span style="--shiki-light:#24292E;--shiki-light-text-decoration:underline;--shiki-dark:#E1E4E8;--shiki-dark-text-decoration:underline;">./images/your-image.png</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">)</span></span></code></pre></div></li></ol><h2 id="添加新图片的步骤" tabindex="-1">添加新图片的步骤 <a class="header-anchor" href="#添加新图片的步骤" aria-label="Permalink to &quot;添加新图片的步骤&quot;">​</a></h2><ol><li>将图片文件放入 <code>blog/public</code> 目录</li><li>在 markdown 文件中使用 <code>/blog/filename.ext</code> 路径引用</li><li>运行 <code>pnpm blog:build</code> 重新构建</li></ol><h2 id="图片优化建议" tabindex="-1">图片优化建议 <a class="header-anchor" href="#图片优化建议" aria-label="Permalink to &quot;图片优化建议&quot;">​</a></h2><ul><li>使用适当的图片格式（PNG、JPG、SVG、WebP）</li><li>压缩图片以减少文件大小</li><li>为图片添加有意义的 alt 文本</li><li>考虑使用响应式图片</li></ul><h2 id="html-标签方式" tabindex="-1">HTML 标签方式 <a class="header-anchor" href="#html-标签方式" aria-label="Permalink to &quot;HTML 标签方式&quot;">​</a></h2><p>你也可以使用 HTML <code>&lt;img&gt;</code> 标签获得更多控制：</p><div class="language-html vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">html</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">img</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> src</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;./images/pic.svg&quot;</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> alt</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;Logo&quot;</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> width</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;100&quot;</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> height</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;100&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> /&gt;</span></span></code></pre></div><p>这样你就可以在博客文章中自由使用图片了！</p></div></div></div><footer class="text-sm font-medium leading-5 divide-y divide-gray-200 dark:divide-slate-200/5 xl:col-start-1 xl:row-start-2"><!----><div class="py-8"><h2 class="text-xs tracking-wide uppercase text-gray-500 dark:text-white"> Previous Article </h2><div class="link"><a href="/blog/posts/2022-year-in-review.html">2022 Year In Review</a></div></div><div class="pt-8"><a class="link" href="/blog/">← Back to the blog</a></div></footer></div></article></main></div></div>
    <script>window.__VP_HASH_MAP__=JSON.parse("{\"index.md\":\"DzYrHPtc\",\"posts_2022-year-in-review.md\":\"CClB9OOf\",\"posts_hello-2021.md\":\"CWsHxIdM\",\"posts_how-to-use-images.md\":\"DriCg1x6\"}");window.__VP_SITE_DATA__=JSON.parse("{\"lang\":\"en-US\",\"dir\":\"ltr\",\"title\":\"The Vue Point\",\"description\":\"The official blog for the Vue.js project\",\"base\":\"/blog/\",\"head\":[],\"router\":{\"prefetchLinks\":true},\"appearance\":true,\"themeConfig\":{},\"locales\":{},\"scrollOffset\":134,\"cleanUrls\":false}");</script>
    
  </body>
</html>