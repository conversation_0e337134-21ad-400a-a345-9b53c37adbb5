<!DOCTYPE html>
<html lang="en-US" dir="ltr">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title>404 | The Vue Point</title>
    <meta name="description" content="Not Found">
    <meta name="generator" content="VitePress v1.6.4">
    <link rel="preload stylesheet" href="/blog/assets/style.BsMwNMTh.css" as="style">
    <link rel="preload stylesheet" href="/blog/vp-icons.css" as="style">
    
    <script type="module" src="/blog/assets/app.BLSFSP5b.js"></script>
    <meta name="twitter:site" content="@vuejs">
    <meta name="twitter:card" content="summary">
    <meta name="twitter:image" content="https://vuejs.org/images/logo.png">
    <script id="check-dark-mode">(()=>{const e=localStorage.getItem("vitepress-theme-appearance")||"auto",a=window.matchMedia("(prefers-color-scheme: dark)").matches;(!e||e==="auto"?a:e==="dark")&&document.documentElement.classList.add("dark")})();</script>
    <script id="check-mac-os">document.documentElement.classList.toggle("mac",/Mac|iPhone|iPod|iPad/i.test(navigator.platform));</script>
  </head>
  <body>
    <div id="app"></div>
    <script>window.__VP_HASH_MAP__=JSON.parse("{\"index.md\":\"DzYrHPtc\",\"posts_2022-year-in-review.md\":\"CClB9OOf\",\"posts_hello-2021.md\":\"CWsHxIdM\",\"posts_how-to-use-images.md\":\"DriCg1x6\"}");window.__VP_SITE_DATA__=JSON.parse("{\"lang\":\"en-US\",\"dir\":\"ltr\",\"title\":\"The Vue Point\",\"description\":\"The official blog for the Vue.js project\",\"base\":\"/blog/\",\"head\":[],\"router\":{\"prefetchLinks\":true},\"appearance\":true,\"themeConfig\":{},\"locales\":{},\"scrollOffset\":134,\"cleanUrls\":false}");</script>
    
  </body>
</html>