---
title: Reflections for 2020-2021
date: 2021-01-11
author: <PERSON>
gravatar: eca93da2c67aadafe35d477aa8f454b8
twitter: '@youyuxi'
---

With a new year upon us, we also have a new blog! In this post, we would like to take a look back at some of the highlights from 2020, as well as some ideas we have for 2021.

---

## Looking Back at 2020

2020 has been a challenging year, to say the least. Nonetheless, the team has made the best of a difficult situation. Despite an already massive user base, Vue's [NPM downloads](https://npm-stat.com/charts.html?package=vue&from=2020-01-01&to=2020-12-31) and [Devtools weekly active users](https://chrome-stats.com/d/nhdogjmejiglipccpnnnanhbledajbpd) both grew close to 50% throughout 2020. As time of this writing, Vue is being downloaded ~8M times per month on NPM and the devtools extension has ~1.5M weekly active users.

Aside from routine maintenance, there are some incredible things that we accomplished as a team:

- [Shipped Vue 3 core](https://github.com/vuejs/vue-next/releases/tag/v3.0.0)
- [Brand new docs site for Vue 3](https://v3.vuejs.org/)
- [Vue Router 4](https://github.com/vuejs/vue-router-next/releases/tag/v4.0.0)
- [Vuex 4 (RC)](https://next.vuex.vuejs.org/)
- [Vue DevTools 6.0 with Vue 3 support (Beta)](https://chrome.google.com/webstore/detail/vuejs-devtools/ljjemllljcmogpfapbkkighbhhppjdbg)
- [Vue Test Utils 2 (Beta)](https://vue-test-utils.vuejs.org/v2/guide/introduction.html)

In addition to iterating on the existing ecosystem, we also invested in exploring improvements on new frontiers:

- New Single File Component (SFC) feature proposals with the goal of leveraging the SFC compiler for more DX and performance wins:
  - [`<script setup>`](https://github.com/vuejs/rfcs/pull/227)
  - [CSS variables injection in `<style>`](https://github.com/vuejs/rfcs/pull/231)
- [VueDX](https://github.com/znck/vue-developer-experience) for providing better IDE integrations and development workflow
- [Vite](http://vitejs.dev/), a new build tool built on top of modern standards
- [VitePress](https://vitepress.vuejs.org/), a new static site generator built on Vue 3 and Vite

In addition to all of these exciting projects, it’s also been incredible to see the community continue to grow despite the challenges 2020 set forth in terms of being unable to facilitate in-person events. With initiatives such as remote conferences, meetups and other events, it’s been a joy to see the community interacting in new ways that might not have been possible otherwise.

## Looking Forward to 2021

While Vue 3 brings many fundamental improvements and opens up avenues for future iterations, we are still in a transition period - both in terms of the library ecosystem and best practices involving new APIs. For early 2021, our focus will be further stabilizing the Vue 3 ecosystem, and continuing to help the community through this transition period. Here is a non-exhaustive list of things we’re looking forward to:

- Stablize current RC/Beta sub projects
- Finalize SFC proposals and further polish IDE support
- Vue 3.1 (Q1, more details as we finalize the release plan)
- Vue 2 → 3 Migration Tools (estimated end of Q1)
- Vue CLI 5 w/ webpack 5 support (estimated Q1)
- ~~Vue 3 IE 11 Compatibility Build (estimated Q2)~~
- Vue 2.7 (estimated Q2/3)
- SSR support in Vite
- Vuex 5 (TBD)

In addition, we are excited about other frameworks and libraries in the ecosystem making progress towards Vue 3 support. And of course, we can’t wait to see what new ideas and tools you all have as we embark on a new chapter in the Vue.js roadmap.
