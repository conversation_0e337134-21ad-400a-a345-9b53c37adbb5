import{_ as t,c as a,o,k as r}from"./chunks/framework._wHCgU2P.js";const c=JSON.parse('{"title":"Reflections for 2020-2021","description":"","frontmatter":{"title":"Reflections for 2020-2021","date":"2021-01-11T00:00:00.000Z","author":"Evan You","gravatar":"eca93da2c67aadafe35d477aa8f454b8","twitter":"@youyuxi"},"headers":[],"relativePath":"posts/hello-2021.md","filePath":"posts/hello-2021.md"}'),i={name:"posts/hello-2021.md"};function s(n,e,l,h,u,p){return o(),a("div",null,e[0]||(e[0]=[r('<p>With a new year upon us, we also have a new blog! In this post, we would like to take a look back at some of the highlights from 2020, as well as some ideas we have for 2021.</p><hr><h2 id="looking-back-at-2020" tabindex="-1">Looking Back at 2020 <a class="header-anchor" href="#looking-back-at-2020" aria-label="Permalink to &quot;Looking Back at 2020&quot;">​</a></h2><p>2020 has been a challenging year, to say the least. Nonetheless, the team has made the best of a difficult situation. Despite an already massive user base, Vue&#39;s <a href="https://npm-stat.com/charts.html?package=vue&amp;from=2020-01-01&amp;to=2020-12-31" target="_blank" rel="noreferrer">NPM downloads</a> and <a href="https://chrome-stats.com/d/nhdogjmejiglipccpnnnanhbledajbpd" target="_blank" rel="noreferrer">Devtools weekly active users</a> both grew close to 50% throughout 2020. As time of this writing, Vue is being downloaded ~8M times per month on NPM and the devtools extension has ~1.5M weekly active users.</p><p>Aside from routine maintenance, there are some incredible things that we accomplished as a team:</p><ul><li><a href="https://github.com/vuejs/vue-next/releases/tag/v3.0.0" target="_blank" rel="noreferrer">Shipped Vue 3 core</a></li><li><a href="https://v3.vuejs.org/" target="_blank" rel="noreferrer">Brand new docs site for Vue 3</a></li><li><a href="https://github.com/vuejs/vue-router-next/releases/tag/v4.0.0" target="_blank" rel="noreferrer">Vue Router 4</a></li><li><a href="https://next.vuex.vuejs.org/" target="_blank" rel="noreferrer">Vuex 4 (RC)</a></li><li><a href="https://chrome.google.com/webstore/detail/vuejs-devtools/ljjemllljcmogpfapbkkighbhhppjdbg" target="_blank" rel="noreferrer">Vue DevTools 6.0 with Vue 3 support (Beta)</a></li><li><a href="https://vue-test-utils.vuejs.org/v2/guide/introduction.html" target="_blank" rel="noreferrer">Vue Test Utils 2 (Beta)</a></li></ul><p>In addition to iterating on the existing ecosystem, we also invested in exploring improvements on new frontiers:</p><ul><li>New Single File Component (SFC) feature proposals with the goal of leveraging the SFC compiler for more DX and performance wins: <ul><li><a href="https://github.com/vuejs/rfcs/pull/227" target="_blank" rel="noreferrer"><code>&lt;script setup&gt;</code></a></li><li><a href="https://github.com/vuejs/rfcs/pull/231" target="_blank" rel="noreferrer">CSS variables injection in <code>&lt;style&gt;</code></a></li></ul></li><li><a href="https://github.com/znck/vue-developer-experience" target="_blank" rel="noreferrer">VueDX</a> for providing better IDE integrations and development workflow</li><li><a href="http://vitejs.dev/" target="_blank" rel="noreferrer">Vite</a>, a new build tool built on top of modern standards</li><li><a href="https://vitepress.vuejs.org/" target="_blank" rel="noreferrer">VitePress</a>, a new static site generator built on Vue 3 and Vite</li></ul><p>In addition to all of these exciting projects, it’s also been incredible to see the community continue to grow despite the challenges 2020 set forth in terms of being unable to facilitate in-person events. With initiatives such as remote conferences, meetups and other events, it’s been a joy to see the community interacting in new ways that might not have been possible otherwise.</p><h2 id="looking-forward-to-2021" tabindex="-1">Looking Forward to 2021 <a class="header-anchor" href="#looking-forward-to-2021" aria-label="Permalink to &quot;Looking Forward to 2021&quot;">​</a></h2><p>While Vue 3 brings many fundamental improvements and opens up avenues for future iterations, we are still in a transition period - both in terms of the library ecosystem and best practices involving new APIs. For early 2021, our focus will be further stabilizing the Vue 3 ecosystem, and continuing to help the community through this transition period. Here is a non-exhaustive list of things we’re looking forward to:</p><ul><li>Stablize current RC/Beta sub projects</li><li>Finalize SFC proposals and further polish IDE support</li><li>Vue 3.1 (Q1, more details as we finalize the release plan)</li><li>Vue 2 → 3 Migration Tools (estimated end of Q1)</li><li>Vue CLI 5 w/ webpack 5 support (estimated Q1)</li><li><s>Vue 3 IE 11 Compatibility Build (estimated Q2)</s></li><li>Vue 2.7 (estimated Q2/3)</li><li>SSR support in Vite</li><li>Vuex 5 (TBD)</li></ul><p>In addition, we are excited about other frameworks and libraries in the ecosystem making progress towards Vue 3 support. And of course, we can’t wait to see what new ideas and tools you all have as we embark on a new chapter in the Vue.js roadmap.</p>',13)]))}const f=t(i,[["render",s]]);export{c as __pageData,f as default};
