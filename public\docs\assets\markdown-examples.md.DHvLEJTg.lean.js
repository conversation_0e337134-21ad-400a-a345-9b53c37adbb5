import{_ as a,c as i,o as n,ae as e}from"./chunks/framework.B-Fvu48Z.js";const E=JSON.parse('{"title":"Markdown Extension Examples","description":"","frontmatter":{},"headers":[],"relativePath":"markdown-examples.md","filePath":"markdown-examples.md"}'),t={name:"markdown-examples.md"};function l(p,s,h,k,r,o){return n(),i("div",null,s[0]||(s[0]=[e("",19)]))}const c=a(t,[["render",l]]);export{E as __pageData,c as default};
