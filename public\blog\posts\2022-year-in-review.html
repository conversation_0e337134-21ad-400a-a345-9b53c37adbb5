<!DOCTYPE html>
<html lang="en-US" dir="ltr">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title>2022 Year In Review | The Vue Point</title>
    <meta name="description" content="The official blog for the Vue.js project">
    <meta name="generator" content="VitePress v1.6.4">
    <link rel="preload stylesheet" href="/blog/assets/style.BsMwNMTh.css" as="style">
    <link rel="preload stylesheet" href="/blog/vp-icons.css" as="style">
    
    <script type="module" src="/blog/assets/app.BLSFSP5b.js"></script>
    <link rel="modulepreload" href="/blog/assets/chunks/theme.C-9tmA-F.js">
    <link rel="modulepreload" href="/blog/assets/chunks/framework._wHCgU2P.js">
    <link rel="modulepreload" href="/blog/assets/posts_2022-year-in-review.md.CClB9OOf.lean.js">
    <meta name="twitter:site" content="@vuejs">
    <meta name="twitter:card" content="summary">
    <meta name="twitter:image" content="https://vuejs.org/images/logo.png">
    <script id="check-dark-mode">(()=>{const e=localStorage.getItem("vitepress-theme-appearance")||"auto",a=window.matchMedia("(prefers-color-scheme: dark)").matches;(!e||e==="auto"?a:e==="dark")&&document.documentElement.classList.add("dark")})();</script>
    <script id="check-mac-os">document.documentElement.classList.toggle("mac",/Mac|iPhone|iPod|iPad/i.test(navigator.platform));</script>
  </head>
  <body>
    <div id="app"><div class="antialiased dark:bg-slate-900"><div class="max-w-3xl mx-auto px-4 sm:px-6 xl:max-w-5xl xl:px-0"><nav class="flex justify-between items-center py-10 font-bold"><a class="text-xl" href="/blog/" aria-label="The Vue Point"><img class="inline-block mr-2" style="width:36px;height:31px;" alt="logo" src="/logo.svg"><span class="hidden md:inline dark:text-white">The Vue Point</span></a><div class="text-sm text-gray-500 dark:text-white leading-5"><a class="hover:text-gray-700 dark:hover:text-gray-200" href="https://github.com/vuejs/blog" target="_blank" rel="noopener"><span class="hidden sm:inline">GitHub </span>Source</a><span class="mr-2 ml-2">·</span><a class="hover:text-gray-700 dark:hover:text-gray-200 vp-raw" href="/feed.rss">RSS<span class="hidden sm:inline"> Feed</span></a><span class="mr-2 ml-2">·</span><a class="hover:text-gray-700 dark:hover:text-gray-200" href="https://vuejs.org" target="_blank" rel="noopener">Vuejs.org →</a></div></nav></div><main class="max-w-3xl mx-auto px-4 sm:px-6 xl:max-w-5xl xl:px-0"><article class="xl:divide-y xl:divide-gray-200 dark:xl:divide-slate-200/5"><header class="pt-6 xl:pb-10 space-y-1 text-center"><dl><dt class="sr-only">Published on</dt><dd class="text-base leading-6 font-medium text-gray-500 dark:text-gray-300"><time datetime="2023-01-01T12:00:00.000Z">January 1, 2023</time></dd></dl><h1 class="text-3xl leading-9 font-extrabold text-gray-900 dark:text-white tracking-tight sm:text-4xl sm:leading-10 md:text-5xl md:leading-14">2022 Year In Review</h1></header><div class="divide-y xl:divide-y-0 divide-gray-200 dark:divide-slate-200/5 xl:grid xl:grid-cols-4 xl:gap-x-10 pb-16 xl:pb-20" style="grid-template-rows:auto 1fr;"><dl class="pt-6 pb-10 xl:pt-11 xl:border-b xl:border-gray-200 dark:xl:border-slate-200/5"><dt class="sr-only">Authors</dt><dd><ul class="flex justify-center xl:block space-x-8 sm:space-x-12 xl:space-x-0 xl:space-y-8"><li class="flex items-center space-x-2"><img src="https://gravatar.com/avatar/eca93da2c67aadafe35d477aa8f454b8" alt="author image" class="w-10 h-10 rounded-full"><dl class="text-sm font-medium leading-5 whitespace-nowrap"><dt class="sr-only">Name</dt><dd class="text-gray-900 dark:text-white">Evan You</dd><dt class="sr-only">Twitter</dt><dd><a href="https://twitter.com/@youyuxi" target="_blank" rel="noopnener noreferrer" class="link">@youyuxi</a></dd></dl></li></ul></dd></dl><div class="divide-y divide-gray-200 dark:divide-slate-200/5 xl:pb-0 xl:col-span-3 xl:row-span-2"><div style="position:relative;" class="prose dark:prose-invert max-w-none pt-10 pb-8"><div><p>Happy new year, Vue community! With 2023 upon us, we would like to take this opportunity to recap what happened in 2022, and discuss what to expect in 2023.</p><hr><h2 id="recap-for-2022" tabindex="-1">Recap for 2022 <a class="header-anchor" href="#recap-for-2022" aria-label="Permalink to &quot;Recap for 2022&quot;">​</a></h2><p>In February 2022, we <a href="./hello-2021.html">switched Vue&#39;s default version to 3.x</a>. The switch marked the readiness of all the official parts of the framework for v3, including a major revamp of the documentation that provides guidance on latest best practices.</p><p>We are still in a transition period for the ecosystem to move to Vue 3. So after the switch, we focused more on improving Vue&#39;s developer experience by investing in tooling. Our team members have been actively involved in the development of <a href="https://vitejs.dev" target="_blank" rel="noreferrer">Vite</a>, and we made significant improvement to Vue&#39;s IDE and TypeScript support by shipping <a href="./hello-2021.html">Volar 1.0</a>.</p><p>Over the course of 2022, we saw the NPM usage of Vue 3 grew by <strong>almost 200%</strong>. On the community side, the Vue 3 ecosystem is now ripe with great solutions to help boost your productivity. Both <a href="https://nuxt.com" target="_blank" rel="noreferrer">Nuxt 3</a> and <a href="https://vuetifyjs.com" target="_blank" rel="noreferrer">Vuetify 3</a> reached stable status in November 2022, and <a href="https://github.com/nativescript-vue/nativescript-vue" target="_blank" rel="noreferrer">NativeScript for Vue 3</a> recently launched beta. In addition, we want to give a shout out to other great projects that had already supported Vue 3 for quite some time: <a href="https://quasar.dev/" target="_blank" rel="noreferrer">Quasar</a>, <a href="https://www.naiveui.com/" target="_blank" rel="noreferrer">NaiveUI</a>, <a href="https://ionicframework.com/docs/vue/overview" target="_blank" rel="noreferrer">Ionic Vue</a>, <a href="https://www.primefaces.org/primevue/" target="_blank" rel="noreferrer">PrimeVue</a>, <a href="https://www.inkline.io/" target="_blank" rel="noreferrer">InkLine</a>, <a href="https://element-plus.org/" target="_blank" rel="noreferrer">ElementPlus</a>, and <a href="https://twitter.com/vuejs/status/1599706412086878208" target="_blank" rel="noreferrer">more</a>.</p><p>Despite Vue 3 being now the default, we understand that many users have to stay on Vue 2 due to the cost of migration. To ensure that Vue 2 users benefit from the advancement of the framework, we decided to move Vue 2&#39;s source code to TypeScript and back-ported some of the most important Vue 3 features in <a href="./hello-2021.html">Vue 2.7</a>. We also made sure that Vite, Vue Devtools and Volar all simultaneously support Vue 2 and Vue 3.</p><h2 id="what-to-expect-in-2023" tabindex="-1">What to Expect in 2023 <a class="header-anchor" href="#what-to-expect-in-2023" aria-label="Permalink to &quot;What to Expect in 2023&quot;">​</a></h2><h3 id="smaller-and-more-frequent-minor-releases" tabindex="-1">Smaller and More Frequent Minor Releases <a class="header-anchor" href="#smaller-and-more-frequent-minor-releases" aria-label="Permalink to &quot;Smaller and More Frequent Minor Releases&quot;">​</a></h3><p>With the last Vue 2 minor release (2.7) out of the door, we expect to be full steam ahead shipping features for Vue 3 core in 2023. We have quite a long list of features that we are excited to work on!</p><p>One thing we would like to improve is our release cadence. Vue follows <a href="https://semver.org/" target="_blank" rel="noreferrer">semver</a>, which means we should only ship features in minor versions. In the past, we did a &quot;big minor&quot; approach where we group many features together in big, infrequent minor releases. This has resulted in quite some low-complexity features being blocked while we worked on other high-complexity ones. In 2023, we want to do smaller and more frequent minor releases so that we can get more features out, faster.</p><p>This also means we will be adjusting what goes into 3.3. Originally, we planned to graduate Suspense and Reactivity Transform from experimental status in 3.3. However, we feel that both still need further RFC discussion, and they should not block other more straightforward features to land. Now, the goal of 3.3 is to land proposed / planned features that are clear wins and do not require RFC discussion - for example, supporting externally imported types in <code>&lt;script setup&gt;</code> macros.</p><p>In parallel to that, we will:</p><ol><li>Further evaluate the readiness of Suspense and Reactivity Transform.</li><li>Spend time to evaluate outstanding user-submitted RFCs and feature requests.</li><li>Post RFCs for features that we intend to land in 3.4 and beyond, for example SSR lazy hydration.</li></ol><p>Expect more details later this month.</p><p>Another thing to note is there is no plan for big breaking changes for the foreseeable future. Acknowledging the challenges users faced during the v2 to v3 transition, we want to have a better long term upgrade story for Vue going forward.</p><h3 id="vapor-mode" tabindex="-1">Vapor Mode <a class="header-anchor" href="#vapor-mode" aria-label="Permalink to &quot;Vapor Mode&quot;">​</a></h3><p>Vapor Mode is an alternative compilation strategy that we have been experimenting with, inspired by <a href="https://www.solidjs.com/" target="_blank" rel="noreferrer">Solid</a>. Given the same Vue SFC, Vapor Mode compiles it into JavaScript output that is more performant, uses less memory, and requires less runtime support code compared to the current Virtual DOM based output. It is still in early phase, but here are some high level points:</p><ul><li><p>Vapor Mode is intended for use cases where performance is the primary concern. It is opt-in and does not affect existing codebases.</p></li><li><p>At the very least, you will be able to embed a Vapor component subtree into any existing Vue 3 app. Ideally, we hope to achieve granular opt-in at the component level, which means freely mixing Vapor and non-Vapor components in the same app.</p></li><li><p>Building an app with only Vapor components allows you to drop the Virtual DOM runtime from the bundle, significantly reducing the baseline runtime size.</p></li><li><p>In order to achieve the best performance, Vapor Mode will only support a subset of Vue features. In particular, Vapor Mode components will only support Composition API and <code>&lt;script setup&gt;</code>. However, this supported subset will work exactly the same between Vapor and non-Vapor components.</p></li></ul><p>We will share more details as we make more progress later in the year.</p><h3 id="conferences" tabindex="-1">Conferences <a class="header-anchor" href="#conferences" aria-label="Permalink to &quot;Conferences&quot;">​</a></h3><p>There are already many in-person Vue conferences lined up for 2023:</p><ul><li><a href="https://vuejs.amsterdam/" target="_blank" rel="noreferrer">Vue.js Amsterdam</a> - Feb 9-10, Amsterdam, The Netherlands</li><li><a href="https://vuejslive.com/" target="_blank" rel="noreferrer">Vue.js Live</a> - May 12 &amp; 15th, London, UK</li><li><a href="https://us.vuejs.org/" target="_blank" rel="noreferrer">VueConf US</a> - May 24-26th, New Orleans, USA</li><li>VueFes Japan - October 28th, Tokyo, Japan (info TBA)</li></ul><p>I (Evan) plan to attend all of these in person. After almost 3 years of absence, I can&#39;t wait to meet the community again - please come say hi!</p><h3 id="one-year-until-vue-2-eol" tabindex="-1">One Year Until Vue 2 EOL <a class="header-anchor" href="#one-year-until-vue-2-eol" aria-label="Permalink to &quot;One Year Until Vue 2 EOL&quot;">​</a></h3><p>As a reminder, today marks <strong>exactly one year until the end of Vue 2 support</strong>. We have created a page explaining the implication of this and outlining the options for those who expect to be using Vue 2 beyond the EOL date: <a href="https://v2.vuejs.org/lts/" target="_blank" rel="noreferrer">Details on Vue 2 EOL and Extended Support</a>.</p></div></div></div><footer class="text-sm font-medium leading-5 divide-y divide-gray-200 dark:divide-slate-200/5 xl:col-start-1 xl:row-start-2"><div class="py-8"><h2 class="text-xs tracking-wide uppercase text-gray-500 dark:text-white"> Next Article </h2><div class="link"><a href="/blog/posts/how-to-use-images.html">如何在博客中使用图片</a></div></div><div class="py-8"><h2 class="text-xs tracking-wide uppercase text-gray-500 dark:text-white"> Previous Article </h2><div class="link"><a href="/blog/posts/hello-2021.html">Reflections for 2020-2021</a></div></div><div class="pt-8"><a class="link" href="/blog/">← Back to the blog</a></div></footer></div></article></main></div></div>
    <script>window.__VP_HASH_MAP__=JSON.parse("{\"index.md\":\"DzYrHPtc\",\"posts_2022-year-in-review.md\":\"CClB9OOf\",\"posts_hello-2021.md\":\"CWsHxIdM\",\"posts_how-to-use-images.md\":\"DriCg1x6\"}");window.__VP_SITE_DATA__=JSON.parse("{\"lang\":\"en-US\",\"dir\":\"ltr\",\"title\":\"The Vue Point\",\"description\":\"The official blog for the Vue.js project\",\"base\":\"/blog/\",\"head\":[],\"router\":{\"prefetchLinks\":true},\"appearance\":true,\"themeConfig\":{},\"locales\":{},\"scrollOffset\":134,\"cleanUrls\":false}");</script>
    
  </body>
</html>