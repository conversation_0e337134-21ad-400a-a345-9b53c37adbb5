<script setup lang="ts">
import { useWindowScroll } from '@vueuse/core'
import { authClient } from "@/utils/auth-client"

const session = authClient.useSession() 

interface NavItem {
  title: string
  href: string
}

withDefaults(defineProps<{
  mobileSecondaryNav?: NavItem[]
}>(), {
  mobileSecondaryNav: () => [],
})

const { y } = useWindowScroll()
// Fix hydration node mismatch
const clientEnv = ref(false)
onMounted(() => {
  clientEnv.value = true
})

const { brand } = useRuntimeConfig().public

async function logOut() {
  await authClient.signOut({
    fetchOptions: {
      onSuccess: () => {
        navigateTo('/')
      },
    },
  });
}

const mobileMainNav = [
  // {
  //   title: 'Pricing',
  //   href: '/pricing',
  // },
  // {
  //   title: 'Blog',
  //   href: '/blog',  // 修正路径为 /blog
  // },
  // {
  //   title: 'Docs',
  //   href: '/docs',
  // },
]
</script>

<template>
  <header
    class="sticky top-0 z-40"
    :class="{'border-b bg-background/75 backdrop-blur-md': clientEnv && y > 50 }"
  >
    <div class="mx-auto w-full max-w-5xl px-6 md:max-w-7xl">
      <!-- mobile nav -->
      <div class="z-20 flex w-full flex-col items-center md:hidden">
        <div class="flex w-full items-center py-4">
          <div class="flex-auto">
            <NuxtLink
              class="text-xl font-semibold tracking-tight"
              href="/"
            >
              {{ brand }}
            </NuxtLink>
          </div>
          <div class="flex flex-auto justify-end">
            <MobileNav
              :nav="mobileMainNav"
              :secondary-nav="mobileSecondaryNav"
            />
          </div>
        </div>
      </div>

      <div class="mx-auto hidden h-[58px] items-center justify-between md:flex">
        <div class="flex-1">
          <NuxtLink
            class="py-1 text-xl font-semibold tracking-tight"
            :aria-label="brand"
            href="/"
          >
            {{ brand }}
          </NuxtLink>
        </div>
        <!-- nav -->
        <!-- <ul class="flex items-center">
          <li>
            <NuxtLink
              class="rounded-md px-2 py-1 text-sm font-semibold text-muted-foreground hover:text-foreground lg:px-4"
              href="/pricing"
            >
              Pricing
            </NuxtLink>
          </li>
          <li>
            <a
              class="rounded-md px-2 py-1 text-sm font-semibold text-muted-foreground hover:text-foreground lg:px-4"
              href="/blog"
            >
              Blog
            </a>
          </li>
          <li>
            <a
              class="rounded-md px-2 py-1 text-sm font-semibold text-muted-foreground hover:text-foreground lg:px-4"
              href="/docs"
            >
              Docs
            </a>
          </li>
        </ul> -->
        <div class="flex flex-1 justify-end gap-4">
          <!-- 明暗模式切换 -->
          <ColorModeSwitch />

            <div v-if="session.data">
              <DropdownMenu>
                <DropdownMenuTrigger class="text-muted-foreground hover:text-foreground">
                  {{ session.data.user.email }} <Icon name="lucide:chevrons-up-down" />
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  class="w-56"
                  align="end"
                >
                  <DropdownMenuLabel class="flex font-normal">
                    <div class="grid flex-1 text-left text-sm leading-tight">
                      <span class="truncate font-semibold">{{ session.data.user.name }}</span>
                      <span class="truncate text-xs text-muted-foreground">{{ session.data.user.email }}</span>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem as-child>
                    <NuxtLink href="/">
                      <Icon
                        name="lucide:slash"
                        class="mr-2"
                      /> Home
                    </NuxtLink>
                  </DropdownMenuItem>
                  <DropdownMenuItem as-child>
                    <NuxtLink href="/dashboard">
                      <Icon
                        name="lucide:layout-dashboard"
                        class="mr-2"
                      /> Dashboard
                    </NuxtLink>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    as-child
                    :disabled="session.data.user.role !== 'ADMIN'"
                  >
                    <NuxtLink href="/admin">
                      <Icon
                        name="lucide:chart-scatter"
                        class="mr-2"
                      /> Admin
                    </NuxtLink>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuGroup>
                    <DropdownMenuItem as-child>
                      <NuxtLink href="/pricing">
                        <Icon
                          name="lucide:sparkles"
                          class="mr-2"
                        /> Upgrade to Pro
                      </NuxtLink>
                    </DropdownMenuItem>
                  </DropdownMenuGroup>
                  <DropdownMenuSeparator />
                  <DropdownMenuGroup>
                    <DropdownMenuItem as-child>
                      <NuxtLink href="/dashboard/settings">
                        Account
                      </NuxtLink>
                    </DropdownMenuItem>
                    <DropdownMenuItem as-child>
                      <NuxtLink href="/dashboard/settings/billing">
                        Billing
                      </NuxtLink>
                    </DropdownMenuItem>
                  </DropdownMenuGroup>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem @click="logOut">
                    <Icon
                      name="lucide:log-out"
                      class="mr-2"
                    />
                    Log out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            <div v-else>
              <Button
                as-child
                variant="ghost"
                class="rounded-full"
                size="lg"
              >
                <NuxtLink
                  class="font-semibold text-muted-foreground hover:text-foreground"
                  href="/auth/login"
                >
                  Sign in
                </NuxtLink>
              </Button>
              <Button
                as-child
                class="rounded-full"
                size="lg"
              >
                <NuxtLink href="/auth/signup">
                  Get Started <Icon
                    name="radix-icons:caret-right"
                    class="-mr-1"
                  /></NuxtLink>
              </Button>
            </div>
        </div>
      </div>
    </div>
  </header>
</template>
