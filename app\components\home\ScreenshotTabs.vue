<script setup lang="ts">
const stacks = [
  {
    icon: 'simple-icons:nuxtdotjs',
    size: 'h-5 w-5',
    title: 'saas',
    description: 'SaaS.',
    src: '/screenshots/placeholder.png',
  },
  {
    icon: 'simple-icons:tailwindcss',
    size: 'h-5 w-5',
    title: 'dashboard',
    description: 'Tailwindcss.',
    src: '/screenshots/placeholder.png',
  },
  {
    icon: 'lineicons:prisma',
    size: 'h-6 w-6',
    title: 'blog',
    description: 'Prisma.',
    src: '/screenshots/placeholder.png',
  },
  {
    icon: 'devicon-plain:supabase',
    size: 'h-5 w-5',
    title: 'admin',
    description: 'Supabase.',
    src: '/screenshots/placeholder.png',
  },
]
</script>

<template>
  <Tabs default-value="saas">
    <TabsList class="h-auto w-full gap-4 bg-transparent px-4">
      <TabsTrigger
        v-for="(stack, index) in stacks"
        :key="index"
        :value="stack.title"
        class="group flex h-10 w-40 items-center gap-2 rounded-lg border p-4 data-[state='active']:bg-gradient-to-b data-[state='active']:from-white/[3%] data-[state='active']:shadow-none"
      >
        <Icon
          name="lucide:slash"
          class="size-3"
        />
        <span>{{ stack.title }}</span>
      </TabsTrigger>
    </TabsList>
    <TabsContent
      v-for="(stack, index) in stacks"
      :key="index"
      :value="stack.title"
    >
      <HomeScreenshot
        :src="stack.src"
        class="mt-8"
      />
    </TabsContent>
  </Tabs>
</template>
