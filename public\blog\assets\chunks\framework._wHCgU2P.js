/**
* @vue/shared v3.5.17
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function is(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const te={},pt=[],je=()=>{},Ni=()=>!1,$t=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),os=e=>e.startsWith("onUpdate:"),pe=Object.assign,ls=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},ji=Object.prototype.hasOwnProperty,Y=(e,t)=>ji.call(e,t),$=Array.isArray,gt=e=>hn(e)==="[object Map]",pr=e=>hn(e)==="[object Set]",V=e=>typeof e=="function",ie=e=>typeof e=="string",Qe=e=>typeof e=="symbol",ne=e=>e!==null&&typeof e=="object",gr=e=>(ne(e)||V(e))&&V(e.then)&&V(e.catch),mr=Object.prototype.toString,hn=e=>mr.call(e),Hi=e=>hn(e).slice(8,-1),_r=e=>hn(e)==="[object Object]",cs=e=>ie(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,mt=is(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),pn=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},$i=/-(\w)/g,Re=pn(e=>e.replace($i,(t,n)=>n?n.toUpperCase():"")),Di=/\B([A-Z])/g,ft=pn(e=>e.replace(Di,"-$1").toLowerCase()),gn=pn(e=>e.charAt(0).toUpperCase()+e.slice(1)),Rn=pn(e=>e?`on${gn(e)}`:""),Xe=(e,t)=>!Object.is(e,t),On=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Jn=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},Ui=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Ps;const mn=()=>Ps||(Ps=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function fs(e){if($(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=ie(s)?ki(s):fs(s);if(r)for(const i in r)t[i]=r[i]}return t}else if(ie(e)||ne(e))return e}const Vi=/;(?![^(]*\))/g,Wi=/:([^]+)/,Bi=/\/\*[^]*?\*\//g;function ki(e){const t={};return e.replace(Bi,"").split(Vi).forEach(n=>{if(n){const s=n.split(Wi);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function as(e){let t="";if(ie(e))t=e;else if($(e))for(let n=0;n<e.length;n++){const s=as(e[n]);s&&(t+=s+" ")}else if(ne(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const Ki="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",qi=is(Ki);function br(e){return!!e||e===""}const yr=e=>!!(e&&e.__v_isRef===!0),Gi=e=>ie(e)?e:e==null?"":$(e)||ne(e)&&(e.toString===mr||!V(e.toString))?yr(e)?Gi(e.value):JSON.stringify(e,vr,2):String(e),vr=(e,t)=>yr(t)?vr(e,t.value):gt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,r],i)=>(n[Mn(s,i)+" =>"]=r,n),{})}:pr(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Mn(n))}:Qe(t)?Mn(t):ne(t)&&!$(t)&&!_r(t)?String(t):t,Mn=(e,t="")=>{var n;return Qe(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ae;class Ji{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=ae,!t&&ae&&(this.index=(ae.scopes||(ae.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=ae;try{return ae=this,t()}finally{ae=n}}}on(){++this._on===1&&(this.prevScope=ae,ae=this)}off(){this._on>0&&--this._on===0&&(ae=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function wr(){return ae}function Xi(e,t=!1){ae&&ae.cleanups.push(e)}let ee;const Pn=new WeakSet;class xr{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,ae&&ae.active&&ae.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Pn.has(this)&&(Pn.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Er(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Is(this),Tr(this);const t=ee,n=Pe;ee=this,Pe=!0;try{return this.fn()}finally{Cr(this),ee=t,Pe=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)hs(t);this.deps=this.depsTail=void 0,Is(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Pn.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Xn(this)&&this.run()}get dirty(){return Xn(this)}}let Sr=0,At,Rt;function Er(e,t=!1){if(e.flags|=8,t){e.next=Rt,Rt=e;return}e.next=At,At=e}function us(){Sr++}function ds(){if(--Sr>0)return;if(Rt){let t=Rt;for(Rt=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;At;){let t=At;for(At=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function Tr(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Cr(e){let t,n=e.depsTail,s=n;for(;s;){const r=s.prevDep;s.version===-1?(s===n&&(n=r),hs(s),zi(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=r}e.deps=t,e.depsTail=n}function Xn(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Ar(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Ar(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===It)||(e.globalVersion=It,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!Xn(e))))return;e.flags|=2;const t=e.dep,n=ee,s=Pe;ee=e,Pe=!0;try{Tr(e);const r=e.fn(e._value);(t.version===0||Xe(r,e._value))&&(e.flags|=128,e._value=r,t.version++)}catch(r){throw t.version++,r}finally{ee=n,Pe=s,Cr(e),e.flags&=-3}}function hs(e,t=!1){const{dep:n,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let i=n.computed.deps;i;i=i.nextDep)hs(i,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function zi(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Pe=!0;const Rr=[];function We(){Rr.push(Pe),Pe=!1}function Be(){const e=Rr.pop();Pe=e===void 0?!0:e}function Is(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=ee;ee=void 0;try{t()}finally{ee=n}}}let It=0;class Yi{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class _n{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!ee||!Pe||ee===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==ee)n=this.activeLink=new Yi(ee,this),ee.deps?(n.prevDep=ee.depsTail,ee.depsTail.nextDep=n,ee.depsTail=n):ee.deps=ee.depsTail=n,Or(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=ee.depsTail,n.nextDep=void 0,ee.depsTail.nextDep=n,ee.depsTail=n,ee.deps===n&&(ee.deps=s)}return n}trigger(t){this.version++,It++,this.notify(t)}notify(t){us();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{ds()}}}function Or(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)Or(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const sn=new WeakMap,nt=Symbol(""),zn=Symbol(""),Ft=Symbol("");function de(e,t,n){if(Pe&&ee){let s=sn.get(e);s||sn.set(e,s=new Map);let r=s.get(n);r||(s.set(n,r=new _n),r.map=s,r.key=n),r.track()}}function Ve(e,t,n,s,r,i){const l=sn.get(e);if(!l){It++;return}const o=c=>{c&&c.trigger()};if(us(),t==="clear")l.forEach(o);else{const c=$(e),d=c&&cs(n);if(c&&n==="length"){const a=Number(s);l.forEach((h,w)=>{(w==="length"||w===Ft||!Qe(w)&&w>=a)&&o(h)})}else switch((n!==void 0||l.has(void 0))&&o(l.get(n)),d&&o(l.get(Ft)),t){case"add":c?d&&o(l.get("length")):(o(l.get(nt)),gt(e)&&o(l.get(zn)));break;case"delete":c||(o(l.get(nt)),gt(e)&&o(l.get(zn)));break;case"set":gt(e)&&o(l.get(nt));break}}ds()}function Qi(e,t){const n=sn.get(e);return n&&n.get(t)}function ut(e){const t=z(e);return t===e?t:(de(t,"iterate",Ft),Ae(e)?t:t.map(ce))}function bn(e){return de(e=z(e),"iterate",Ft),e}const Zi={__proto__:null,[Symbol.iterator](){return In(this,Symbol.iterator,ce)},concat(...e){return ut(this).concat(...e.map(t=>$(t)?ut(t):t))},entries(){return In(this,"entries",e=>(e[1]=ce(e[1]),e))},every(e,t){return De(this,"every",e,t,void 0,arguments)},filter(e,t){return De(this,"filter",e,t,n=>n.map(ce),arguments)},find(e,t){return De(this,"find",e,t,ce,arguments)},findIndex(e,t){return De(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return De(this,"findLast",e,t,ce,arguments)},findLastIndex(e,t){return De(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return De(this,"forEach",e,t,void 0,arguments)},includes(...e){return Fn(this,"includes",e)},indexOf(...e){return Fn(this,"indexOf",e)},join(e){return ut(this).join(e)},lastIndexOf(...e){return Fn(this,"lastIndexOf",e)},map(e,t){return De(this,"map",e,t,void 0,arguments)},pop(){return Et(this,"pop")},push(...e){return Et(this,"push",e)},reduce(e,...t){return Fs(this,"reduce",e,t)},reduceRight(e,...t){return Fs(this,"reduceRight",e,t)},shift(){return Et(this,"shift")},some(e,t){return De(this,"some",e,t,void 0,arguments)},splice(...e){return Et(this,"splice",e)},toReversed(){return ut(this).toReversed()},toSorted(e){return ut(this).toSorted(e)},toSpliced(...e){return ut(this).toSpliced(...e)},unshift(...e){return Et(this,"unshift",e)},values(){return In(this,"values",ce)}};function In(e,t,n){const s=bn(e),r=s[t]();return s!==e&&!Ae(e)&&(r._next=r.next,r.next=()=>{const i=r._next();return i.value&&(i.value=n(i.value)),i}),r}const eo=Array.prototype;function De(e,t,n,s,r,i){const l=bn(e),o=l!==e&&!Ae(e),c=l[t];if(c!==eo[t]){const h=c.apply(e,i);return o?ce(h):h}let d=n;l!==e&&(o?d=function(h,w){return n.call(this,ce(h),w,e)}:n.length>2&&(d=function(h,w){return n.call(this,h,w,e)}));const a=c.call(l,d,s);return o&&r?r(a):a}function Fs(e,t,n,s){const r=bn(e);let i=n;return r!==e&&(Ae(e)?n.length>3&&(i=function(l,o,c){return n.call(this,l,o,c,e)}):i=function(l,o,c){return n.call(this,l,ce(o),c,e)}),r[t](i,...s)}function Fn(e,t,n){const s=z(e);de(s,"iterate",Ft);const r=s[t](...n);return(r===-1||r===!1)&&ms(n[0])?(n[0]=z(n[0]),s[t](...n)):r}function Et(e,t,n=[]){We(),us();const s=z(e)[t].apply(e,n);return ds(),Be(),s}const to=is("__proto__,__v_isRef,__isVue"),Mr=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Qe));function no(e){Qe(e)||(e=String(e));const t=z(this);return de(t,"has",e),t.hasOwnProperty(e)}class Pr{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const r=this._isReadonly,i=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return i;if(n==="__v_raw")return s===(r?i?ho:Nr:i?Lr:Fr).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const l=$(t);if(!r){let c;if(l&&(c=Zi[n]))return c;if(n==="hasOwnProperty")return no}const o=Reflect.get(t,n,le(t)?t:s);return(Qe(n)?Mr.has(n):to(n))||(r||de(t,"get",n),i)?o:le(o)?l&&cs(n)?o:o.value:ne(o)?r?vn(o):yn(o):o}}class Ir extends Pr{constructor(t=!1){super(!1,t)}set(t,n,s,r){let i=t[n];if(!this._isShallow){const c=Ye(i);if(!Ae(s)&&!Ye(s)&&(i=z(i),s=z(s)),!$(t)&&le(i)&&!le(s))return c?!1:(i.value=s,!0)}const l=$(t)&&cs(n)?Number(n)<t.length:Y(t,n),o=Reflect.set(t,n,s,le(t)?t:r);return t===z(r)&&(l?Xe(s,i)&&Ve(t,"set",n,s):Ve(t,"add",n,s)),o}deleteProperty(t,n){const s=Y(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&s&&Ve(t,"delete",n,void 0),r}has(t,n){const s=Reflect.has(t,n);return(!Qe(n)||!Mr.has(n))&&de(t,"has",n),s}ownKeys(t){return de(t,"iterate",$(t)?"length":nt),Reflect.ownKeys(t)}}class so extends Pr{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const ro=new Ir,io=new so,oo=new Ir(!0);const Yn=e=>e,kt=e=>Reflect.getPrototypeOf(e);function lo(e,t,n){return function(...s){const r=this.__v_raw,i=z(r),l=gt(i),o=e==="entries"||e===Symbol.iterator&&l,c=e==="keys"&&l,d=r[e](...s),a=n?Yn:t?rn:ce;return!t&&de(i,"iterate",c?zn:nt),{next(){const{value:h,done:w}=d.next();return w?{value:h,done:w}:{value:o?[a(h[0]),a(h[1])]:a(h),done:w}},[Symbol.iterator](){return this}}}}function Kt(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function co(e,t){const n={get(r){const i=this.__v_raw,l=z(i),o=z(r);e||(Xe(r,o)&&de(l,"get",r),de(l,"get",o));const{has:c}=kt(l),d=t?Yn:e?rn:ce;if(c.call(l,r))return d(i.get(r));if(c.call(l,o))return d(i.get(o));i!==l&&i.get(r)},get size(){const r=this.__v_raw;return!e&&de(z(r),"iterate",nt),Reflect.get(r,"size",r)},has(r){const i=this.__v_raw,l=z(i),o=z(r);return e||(Xe(r,o)&&de(l,"has",r),de(l,"has",o)),r===o?i.has(r):i.has(r)||i.has(o)},forEach(r,i){const l=this,o=l.__v_raw,c=z(o),d=t?Yn:e?rn:ce;return!e&&de(c,"iterate",nt),o.forEach((a,h)=>r.call(i,d(a),d(h),l))}};return pe(n,e?{add:Kt("add"),set:Kt("set"),delete:Kt("delete"),clear:Kt("clear")}:{add(r){!t&&!Ae(r)&&!Ye(r)&&(r=z(r));const i=z(this);return kt(i).has.call(i,r)||(i.add(r),Ve(i,"add",r,r)),this},set(r,i){!t&&!Ae(i)&&!Ye(i)&&(i=z(i));const l=z(this),{has:o,get:c}=kt(l);let d=o.call(l,r);d||(r=z(r),d=o.call(l,r));const a=c.call(l,r);return l.set(r,i),d?Xe(i,a)&&Ve(l,"set",r,i):Ve(l,"add",r,i),this},delete(r){const i=z(this),{has:l,get:o}=kt(i);let c=l.call(i,r);c||(r=z(r),c=l.call(i,r)),o&&o.call(i,r);const d=i.delete(r);return c&&Ve(i,"delete",r,void 0),d},clear(){const r=z(this),i=r.size!==0,l=r.clear();return i&&Ve(r,"clear",void 0,void 0),l}}),["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=lo(r,e,t)}),n}function ps(e,t){const n=co(e,t);return(s,r,i)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?s:Reflect.get(Y(n,r)&&r in s?n:s,r,i)}const fo={get:ps(!1,!1)},ao={get:ps(!1,!0)},uo={get:ps(!0,!1)};const Fr=new WeakMap,Lr=new WeakMap,Nr=new WeakMap,ho=new WeakMap;function po(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function go(e){return e.__v_skip||!Object.isExtensible(e)?0:po(Hi(e))}function yn(e){return Ye(e)?e:gs(e,!1,ro,fo,Fr)}function mo(e){return gs(e,!1,oo,ao,Lr)}function vn(e){return gs(e,!0,io,uo,Nr)}function gs(e,t,n,s,r){if(!ne(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=go(e);if(i===0)return e;const l=r.get(e);if(l)return l;const o=new Proxy(e,i===2?s:n);return r.set(e,o),o}function st(e){return Ye(e)?st(e.__v_raw):!!(e&&e.__v_isReactive)}function Ye(e){return!!(e&&e.__v_isReadonly)}function Ae(e){return!!(e&&e.__v_isShallow)}function ms(e){return e?!!e.__v_raw:!1}function z(e){const t=e&&e.__v_raw;return t?z(t):e}function en(e){return!Y(e,"__v_skip")&&Object.isExtensible(e)&&Jn(e,"__v_skip",!0),e}const ce=e=>ne(e)?yn(e):e,rn=e=>ne(e)?vn(e):e;function le(e){return e?e.__v_isRef===!0:!1}function rt(e){return jr(e,!1)}function _t(e){return jr(e,!0)}function jr(e,t){return le(e)?e:new _o(e,t)}class _o{constructor(t,n){this.dep=new _n,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:z(t),this._value=n?t:ce(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||Ae(t)||Ye(t);t=s?t:z(t),Xe(t,n)&&(this._rawValue=t,this._value=s?t:ce(t),this.dep.trigger())}}function _s(e){return le(e)?e.value:e}function ze(e){return V(e)?e():_s(e)}const bo={get:(e,t,n)=>t==="__v_raw"?e:_s(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return le(r)&&!le(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function Hr(e){return st(e)?e:new Proxy(e,bo)}class yo{constructor(t){this.__v_isRef=!0,this._value=void 0;const n=this.dep=new _n,{get:s,set:r}=t(n.track.bind(n),n.trigger.bind(n));this._get=s,this._set=r}get value(){return this._value=this._get()}set value(t){this._set(t)}}function vo(e){return new yo(e)}class wo{constructor(t,n,s){this._object=t,this._key=n,this._defaultValue=s,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Qi(z(this._object),this._key)}}class xo{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function So(e,t,n){return le(e)?e:V(e)?new xo(e):ne(e)&&arguments.length>1?Eo(e,t,n):rt(e)}function Eo(e,t,n){const s=e[t];return le(s)?s:new wo(e,t,n)}class To{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new _n(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=It-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&ee!==this)return Er(this,!0),!0}get value(){const t=this.dep.track();return Ar(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Co(e,t,n=!1){let s,r;return V(e)?s=e:(s=e.get,r=e.set),new To(s,r,n)}const qt={},on=new WeakMap;let tt;function Ao(e,t=!1,n=tt){if(n){let s=on.get(n);s||on.set(n,s=[]),s.push(e)}}function Ro(e,t,n=te){const{immediate:s,deep:r,once:i,scheduler:l,augmentJob:o,call:c}=n,d=g=>r?g:Ae(g)||r===!1||r===0?Je(g,1):Je(g);let a,h,w,x,O=!1,R=!1;if(le(e)?(h=()=>e.value,O=Ae(e)):st(e)?(h=()=>d(e),O=!0):$(e)?(R=!0,O=e.some(g=>st(g)||Ae(g)),h=()=>e.map(g=>{if(le(g))return g.value;if(st(g))return d(g);if(V(g))return c?c(g,2):g()})):V(e)?t?h=c?()=>c(e,2):e:h=()=>{if(w){We();try{w()}finally{Be()}}const g=tt;tt=a;try{return c?c(e,3,[x]):e(x)}finally{tt=g}}:h=je,t&&r){const g=h,A=r===!0?1/0:r;h=()=>Je(g(),A)}const J=wr(),j=()=>{a.stop(),J&&J.active&&ls(J.effects,a)};if(i&&t){const g=t;t=(...A)=>{g(...A),j()}}let K=R?new Array(e.length).fill(qt):qt;const p=g=>{if(!(!(a.flags&1)||!a.dirty&&!g))if(t){const A=a.run();if(r||O||(R?A.some((B,U)=>Xe(B,K[U])):Xe(A,K))){w&&w();const B=tt;tt=a;try{const U=[A,K===qt?void 0:R&&K[0]===qt?[]:K,x];K=A,c?c(t,3,U):t(...U)}finally{tt=B}}}else a.run()};return o&&o(p),a=new xr(h),a.scheduler=l?()=>l(p,!1):p,x=g=>Ao(g,!1,a),w=a.onStop=()=>{const g=on.get(a);if(g){if(c)c(g,4);else for(const A of g)A();on.delete(a)}},t?s?p(!0):K=a.run():l?l(p.bind(null,!0),!0):a.run(),j.pause=a.pause.bind(a),j.resume=a.resume.bind(a),j.stop=j,j}function Je(e,t=1/0,n){if(t<=0||!ne(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,le(e))Je(e.value,t,n);else if($(e))for(let s=0;s<e.length;s++)Je(e[s],t,n);else if(pr(e)||gt(e))e.forEach(s=>{Je(s,t,n)});else if(_r(e)){for(const s in e)Je(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&Je(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Dt(e,t,n,s){try{return s?e(...s):e()}catch(r){wn(r,t,n)}}function $e(e,t,n,s){if(V(e)){const r=Dt(e,t,n,s);return r&&gr(r)&&r.catch(i=>{wn(i,t,n)}),r}if($(e)){const r=[];for(let i=0;i<e.length;i++)r.push($e(e[i],t,n,s));return r}}function wn(e,t,n,s=!0){const r=t?t.vnode:null,{errorHandler:i,throwUnhandledErrorInProduction:l}=t&&t.appContext.config||te;if(t){let o=t.parent;const c=t.proxy,d=`https://vuejs.org/error-reference/#runtime-${n}`;for(;o;){const a=o.ec;if(a){for(let h=0;h<a.length;h++)if(a[h](e,c,d)===!1)return}o=o.parent}if(i){We(),Dt(i,null,10,[e,c,d]),Be();return}}Oo(e,n,r,s,l)}function Oo(e,t,n,s=!0,r=!1){if(r)throw e;console.error(e)}const me=[];let Le=-1;const bt=[];let qe=null,ht=0;const $r=Promise.resolve();let ln=null;function xn(e){const t=ln||$r;return e?t.then(this?e.bind(this):e):t}function Mo(e){let t=Le+1,n=me.length;for(;t<n;){const s=t+n>>>1,r=me[s],i=Lt(r);i<e||i===e&&r.flags&2?t=s+1:n=s}return t}function bs(e){if(!(e.flags&1)){const t=Lt(e),n=me[me.length-1];!n||!(e.flags&2)&&t>=Lt(n)?me.push(e):me.splice(Mo(t),0,e),e.flags|=1,Dr()}}function Dr(){ln||(ln=$r.then(Ur))}function Po(e){$(e)?bt.push(...e):qe&&e.id===-1?qe.splice(ht+1,0,e):e.flags&1||(bt.push(e),e.flags|=1),Dr()}function Ls(e,t,n=Le+1){for(;n<me.length;n++){const s=me[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;me.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function cn(e){if(bt.length){const t=[...new Set(bt)].sort((n,s)=>Lt(n)-Lt(s));if(bt.length=0,qe){qe.push(...t);return}for(qe=t,ht=0;ht<qe.length;ht++){const n=qe[ht];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}qe=null,ht=0}}const Lt=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Ur(e){try{for(Le=0;Le<me.length;Le++){const t=me[Le];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Dt(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Le<me.length;Le++){const t=me[Le];t&&(t.flags&=-2)}Le=-1,me.length=0,cn(),ln=null,(me.length||bt.length)&&Ur()}}let xe=null,Vr=null;function fn(e){const t=xe;return xe=e,Vr=e&&e.type.__scopeId||null,t}function Io(e,t=xe,n){if(!t||e._n)return e;const s=(...r)=>{s._d&&Ks(-1);const i=fn(t);let l;try{l=e(...r)}finally{fn(i),s._d&&Ks(1)}return l};return s._n=!0,s._c=!0,s._d=!0,s}function Ne(e,t,n,s){const r=e.dirs,i=t&&t.dirs;for(let l=0;l<r.length;l++){const o=r[l];i&&(o.oldValue=i[l].value);let c=o.dir[s];c&&(We(),$e(c,n,8,[e.el,o,e,t]),Be())}}const Fo=Symbol("_vte"),Lo=e=>e.__isTeleport;function ys(e,t){e.shapeFlag&6&&e.component?(e.transition=t,ys(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function Wr(e,t){return V(e)?pe({name:e.name},t,{setup:e}):e}function Br(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function yt(e,t,n,s,r=!1){if($(e)){e.forEach((O,R)=>yt(O,t&&($(t)?t[R]:t),n,s,r));return}if(vt(s)&&!r){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&yt(e,t,n,s.component.subTree);return}const i=s.shapeFlag&4?Cs(s.component):s.el,l=r?null:i,{i:o,r:c}=e,d=t&&t.r,a=o.refs===te?o.refs={}:o.refs,h=o.setupState,w=z(h),x=h===te?()=>!1:O=>Y(w,O);if(d!=null&&d!==c&&(ie(d)?(a[d]=null,x(d)&&(h[d]=null)):le(d)&&(d.value=null)),V(c))Dt(c,o,12,[l,a]);else{const O=ie(c),R=le(c);if(O||R){const J=()=>{if(e.f){const j=O?x(c)?h[c]:a[c]:c.value;r?$(j)&&ls(j,i):$(j)?j.includes(i)||j.push(i):O?(a[c]=[i],x(c)&&(h[c]=a[c])):(c.value=[i],e.k&&(a[e.k]=c.value))}else O?(a[c]=l,x(c)&&(h[c]=l)):R&&(c.value=l,e.k&&(a[e.k]=l))};l?(J.id=-1,we(J,n)):J()}}}let Ns=!1;const dt=()=>{Ns||(console.error("Hydration completed but contains mismatches."),Ns=!0)},No=e=>e.namespaceURI.includes("svg")&&e.tagName!=="foreignObject",jo=e=>e.namespaceURI.includes("MathML"),Gt=e=>{if(e.nodeType===1){if(No(e))return"svg";if(jo(e))return"mathml"}},Jt=e=>e.nodeType===8;function Ho(e){const{mt:t,p:n,o:{patchProp:s,createText:r,nextSibling:i,parentNode:l,remove:o,insert:c,createComment:d}}=e,a=(p,g)=>{if(!g.hasChildNodes()){n(null,p,g),cn(),g._vnode=p;return}h(g.firstChild,p,null,null,null),cn(),g._vnode=p},h=(p,g,A,B,U,q=!1)=>{q=q||!!g.dynamicChildren;const k=Jt(p)&&p.data==="[",L=()=>R(p,g,A,B,U,k),{type:M,ref:D,shapeFlag:W,patchFlag:Ee}=g;let _e=p.nodeType;g.el=p,Ee===-2&&(q=!1,g.dynamicChildren=null);let N=null;switch(M){case lt:_e!==3?g.children===""?(c(g.el=r(""),l(p),p),N=p):N=L():(p.data!==g.children&&(dt(),p.data=g.children),N=i(p));break;case ke:K(p)?(N=i(p),j(g.el=p.content.firstChild,p,A)):_e!==8||k?N=L():N=i(p);break;case Mt:if(k&&(p=i(p),_e=p.nodeType),_e===1||_e===3){N=p;const G=!g.children.length;for(let I=0;I<g.staticCount;I++)G&&(g.children+=N.nodeType===1?N.outerHTML:N.data),I===g.staticCount-1&&(g.anchor=N),N=i(N);return k?i(N):N}else L();break;case Me:k?N=O(p,g,A,B,U,q):N=L();break;default:if(W&1)(_e!==1||g.type.toLowerCase()!==p.tagName.toLowerCase())&&!K(p)?N=L():N=w(p,g,A,B,U,q);else if(W&6){g.slotScopeIds=U;const G=l(p);if(k?N=J(p):Jt(p)&&p.data==="teleport start"?N=J(p,p.data,"teleport end"):N=i(p),t(g,G,null,A,B,Gt(G),q),vt(g)&&!g.type.__asyncResolved){let I;k?(I=he(Me),I.anchor=N?N.previousSibling:G.lastChild):I=p.nodeType===3?mi(""):he("div"),I.el=p,g.component.subTree=I}}else W&64?_e!==8?N=L():N=g.type.hydrate(p,g,A,B,U,q,e,x):W&128&&(N=g.type.hydrate(p,g,A,B,Gt(l(p)),U,q,e,h))}return D!=null&&yt(D,null,B,g),N},w=(p,g,A,B,U,q)=>{q=q||!!g.dynamicChildren;const{type:k,props:L,patchFlag:M,shapeFlag:D,dirs:W,transition:Ee}=g,_e=k==="input"||k==="option";if(_e||M!==-1){W&&Ne(g,null,A,"created");let N=!1;if(K(p)){N=ri(null,Ee)&&A&&A.vnode.props&&A.vnode.props.appear;const I=p.content.firstChild;if(N){const re=I.getAttribute("class");re&&(I.$cls=re),Ee.beforeEnter(I)}j(I,p,A),g.el=p=I}if(D&16&&!(L&&(L.innerHTML||L.textContent))){let I=x(p.firstChild,g,p,A,B,U,q);for(;I;){Xt(p,1)||dt();const re=I;I=I.nextSibling,o(re)}}else if(D&8){let I=g.children;I[0]===`
`&&(p.tagName==="PRE"||p.tagName==="TEXTAREA")&&(I=I.slice(1)),p.textContent!==I&&(Xt(p,0)||dt(),p.textContent=g.children)}if(L){if(_e||!q||M&48){const I=p.tagName.includes("-");for(const re in L)(_e&&(re.endsWith("value")||re==="indeterminate")||$t(re)&&!mt(re)||re[0]==="."||I)&&s(p,re,null,L[re],void 0,A)}else if(L.onClick)s(p,"onClick",null,L.onClick,void 0,A);else if(M&4&&st(L.style))for(const I in L.style)L.style[I]}let G;(G=L&&L.onVnodeBeforeMount)&&Te(G,A,g),W&&Ne(g,null,A,"beforeMount"),((G=L&&L.onVnodeMounted)||W||N)&&di(()=>{G&&Te(G,A,g),N&&Ee.enter(p),W&&Ne(g,null,A,"mounted")},B)}return p.nextSibling},x=(p,g,A,B,U,q,k)=>{k=k||!!g.dynamicChildren;const L=g.children,M=L.length;for(let D=0;D<M;D++){const W=k?L[D]:L[D]=Ce(L[D]),Ee=W.type===lt;p?(Ee&&!k&&D+1<M&&Ce(L[D+1]).type===lt&&(c(r(p.data.slice(W.children.length)),A,i(p)),p.data=W.children),p=h(p,W,B,U,q,k)):Ee&&!W.children?c(W.el=r(""),A):(Xt(A,1)||dt(),n(null,W,A,null,B,U,Gt(A),q))}return p},O=(p,g,A,B,U,q)=>{const{slotScopeIds:k}=g;k&&(U=U?U.concat(k):k);const L=l(p),M=x(i(p),g,L,A,B,U,q);return M&&Jt(M)&&M.data==="]"?i(g.anchor=M):(dt(),c(g.anchor=d("]"),L,M),M)},R=(p,g,A,B,U,q)=>{if(Xt(p.parentElement,1)||dt(),g.el=null,q){const M=J(p);for(;;){const D=i(p);if(D&&D!==M)o(D);else break}}const k=i(p),L=l(p);return o(p),n(null,g,L,k,A,B,Gt(L),U),A&&(A.vnode.el=g.el,ai(A,g.el)),k},J=(p,g="[",A="]")=>{let B=0;for(;p;)if(p=i(p),p&&Jt(p)&&(p.data===g&&B++,p.data===A)){if(B===0)return i(p);B--}return p},j=(p,g,A)=>{const B=g.parentNode;B&&B.replaceChild(p,g);let U=A;for(;U;)U.vnode.el===g&&(U.vnode.el=U.subTree.el=p),U=U.parent},K=p=>p.nodeType===1&&p.tagName==="TEMPLATE";return[a,h]}const js="data-allow-mismatch",$o={0:"text",1:"children",2:"class",3:"style",4:"attribute"};function Xt(e,t){if(t===0||t===1)for(;e&&!e.hasAttribute(js);)e=e.parentElement;const n=e&&e.getAttribute(js);if(n==null)return!1;if(n==="")return!0;{const s=n.split(",");return t===0&&s.includes("children")?!0:s.includes($o[t])}}mn().requestIdleCallback;mn().cancelIdleCallback;const vt=e=>!!e.type.__asyncLoader,kr=e=>e.type.__isKeepAlive;function Do(e,t){Kr(e,"a",t)}function Uo(e,t){Kr(e,"da",t)}function Kr(e,t,n=oe){const s=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(Sn(t,s,n),n){let r=n.parent;for(;r&&r.parent;)kr(r.parent.vnode)&&Vo(s,t,n,r),r=r.parent}}function Vo(e,t,n,s){const r=Sn(t,e,s,!0);vs(()=>{ls(s[t],r)},n)}function Sn(e,t,n=oe,s=!1){if(n){const r=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...l)=>{We();const o=Vt(n),c=$e(t,n,e,l);return o(),Be(),c});return s?r.unshift(i):r.push(i),i}}const Ke=e=>(t,n=oe)=>{(!jt||e==="sp")&&Sn(e,(...s)=>t(...s),n)},Wo=Ke("bm"),Ut=Ke("m"),Bo=Ke("bu"),ko=Ke("u"),Ko=Ke("bum"),vs=Ke("um"),qo=Ke("sp"),Go=Ke("rtg"),Jo=Ke("rtc");function Xo(e,t=oe){Sn("ec",e,t)}const zo="components";function df(e,t){return Qo(zo,e,!0,t)||e}const Yo=Symbol.for("v-ndc");function Qo(e,t,n=!0,s=!1){const r=xe||oe;if(r){const i=r.type;{const o=$l(i,!1);if(o&&(o===t||o===Re(t)||o===gn(Re(t))))return i}const l=Hs(r[e]||i[e],t)||Hs(r.appContext[e],t);return!l&&s?i:l}}function Hs(e,t){return e&&(e[t]||e[Re(t)]||e[gn(Re(t))])}function hf(e,t,n,s){let r;const i=n,l=$(e);if(l||ie(e)){const o=l&&st(e);let c=!1,d=!1;o&&(c=!Ae(e),d=Ye(e),e=bn(e)),r=new Array(e.length);for(let a=0,h=e.length;a<h;a++)r[a]=t(c?d?rn(ce(e[a])):ce(e[a]):e[a],a,void 0,i)}else if(typeof e=="number"){r=new Array(e);for(let o=0;o<e;o++)r[o]=t(o+1,o,void 0,i)}else if(ne(e))if(e[Symbol.iterator])r=Array.from(e,(o,c)=>t(o,c,void 0,i));else{const o=Object.keys(e);r=new Array(o.length);for(let c=0,d=o.length;c<d;c++){const a=o[c];r[c]=t(e[a],a,c,i)}}else r=[];return r}const Qn=e=>e?_i(e)?Cs(e):Qn(e.parent):null,Ot=pe(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Qn(e.parent),$root:e=>Qn(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Gr(e),$forceUpdate:e=>e.f||(e.f=()=>{bs(e.update)}),$nextTick:e=>e.n||(e.n=xn.bind(e.proxy)),$watch:e=>yl.bind(e)}),Ln=(e,t)=>e!==te&&!e.__isScriptSetup&&Y(e,t),Zo={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:r,props:i,accessCache:l,type:o,appContext:c}=e;let d;if(t[0]!=="$"){const x=l[t];if(x!==void 0)switch(x){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return i[t]}else{if(Ln(s,t))return l[t]=1,s[t];if(r!==te&&Y(r,t))return l[t]=2,r[t];if((d=e.propsOptions[0])&&Y(d,t))return l[t]=3,i[t];if(n!==te&&Y(n,t))return l[t]=4,n[t];Zn&&(l[t]=0)}}const a=Ot[t];let h,w;if(a)return t==="$attrs"&&de(e.attrs,"get",""),a(e);if((h=o.__cssModules)&&(h=h[t]))return h;if(n!==te&&Y(n,t))return l[t]=4,n[t];if(w=c.config.globalProperties,Y(w,t))return w[t]},set({_:e},t,n){const{data:s,setupState:r,ctx:i}=e;return Ln(r,t)?(r[t]=n,!0):s!==te&&Y(s,t)?(s[t]=n,!0):Y(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:i}},l){let o;return!!n[l]||e!==te&&Y(e,l)||Ln(t,l)||(o=i[0])&&Y(o,l)||Y(s,l)||Y(Ot,l)||Y(r.config.globalProperties,l)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:Y(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function $s(e){return $(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Zn=!0;function el(e){const t=Gr(e),n=e.proxy,s=e.ctx;Zn=!1,t.beforeCreate&&Ds(t.beforeCreate,e,"bc");const{data:r,computed:i,methods:l,watch:o,provide:c,inject:d,created:a,beforeMount:h,mounted:w,beforeUpdate:x,updated:O,activated:R,deactivated:J,beforeDestroy:j,beforeUnmount:K,destroyed:p,unmounted:g,render:A,renderTracked:B,renderTriggered:U,errorCaptured:q,serverPrefetch:k,expose:L,inheritAttrs:M,components:D,directives:W,filters:Ee}=t;if(d&&tl(d,s,null),l)for(const G in l){const I=l[G];V(I)&&(s[G]=I.bind(n))}if(r){const G=r.call(n,n);ne(G)&&(e.data=yn(G))}if(Zn=!0,i)for(const G in i){const I=i[G],re=V(I)?I.bind(n,n):V(I.get)?I.get.bind(n,n):je,Wt=!V(I)&&V(I.set)?I.set.bind(n):je,Ze=se({get:re,set:Wt});Object.defineProperty(s,G,{enumerable:!0,configurable:!0,get:()=>Ze.value,set:Ie=>Ze.value=Ie})}if(o)for(const G in o)qr(o[G],s,n,G);if(c){const G=V(c)?c.call(n):c;Reflect.ownKeys(G).forEach(I=>{ll(I,G[I])})}a&&Ds(a,e,"c");function N(G,I){$(I)?I.forEach(re=>G(re.bind(n))):I&&G(I.bind(n))}if(N(Wo,h),N(Ut,w),N(Bo,x),N(ko,O),N(Do,R),N(Uo,J),N(Xo,q),N(Jo,B),N(Go,U),N(Ko,K),N(vs,g),N(qo,k),$(L))if(L.length){const G=e.exposed||(e.exposed={});L.forEach(I=>{Object.defineProperty(G,I,{get:()=>n[I],set:re=>n[I]=re})})}else e.exposed||(e.exposed={});A&&e.render===je&&(e.render=A),M!=null&&(e.inheritAttrs=M),D&&(e.components=D),W&&(e.directives=W),k&&Br(e)}function tl(e,t,n=je){$(e)&&(e=es(e));for(const s in e){const r=e[s];let i;ne(r)?"default"in r?i=ot(r.from||s,r.default,!0):i=ot(r.from||s):i=ot(r),le(i)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>i.value,set:l=>i.value=l}):t[s]=i}}function Ds(e,t,n){$e($(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function qr(e,t,n,s){let r=s.includes(".")?ci(n,s):()=>n[s];if(ie(e)){const i=t[e];V(i)&&He(r,i)}else if(V(e))He(r,e.bind(n));else if(ne(e))if($(e))e.forEach(i=>qr(i,t,n,s));else{const i=V(e.handler)?e.handler.bind(n):t[e.handler];V(i)&&He(r,i,e)}}function Gr(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:l}}=e.appContext,o=i.get(t);let c;return o?c=o:!r.length&&!n&&!s?c=t:(c={},r.length&&r.forEach(d=>an(c,d,l,!0)),an(c,t,l)),ne(t)&&i.set(t,c),c}function an(e,t,n,s=!1){const{mixins:r,extends:i}=t;i&&an(e,i,n,!0),r&&r.forEach(l=>an(e,l,n,!0));for(const l in t)if(!(s&&l==="expose")){const o=nl[l]||n&&n[l];e[l]=o?o(e[l],t[l]):t[l]}return e}const nl={data:Us,props:Vs,emits:Vs,methods:Ct,computed:Ct,beforeCreate:ge,created:ge,beforeMount:ge,mounted:ge,beforeUpdate:ge,updated:ge,beforeDestroy:ge,beforeUnmount:ge,destroyed:ge,unmounted:ge,activated:ge,deactivated:ge,errorCaptured:ge,serverPrefetch:ge,components:Ct,directives:Ct,watch:rl,provide:Us,inject:sl};function Us(e,t){return t?e?function(){return pe(V(e)?e.call(this,this):e,V(t)?t.call(this,this):t)}:t:e}function sl(e,t){return Ct(es(e),es(t))}function es(e){if($(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function ge(e,t){return e?[...new Set([].concat(e,t))]:t}function Ct(e,t){return e?pe(Object.create(null),e,t):t}function Vs(e,t){return e?$(e)&&$(t)?[...new Set([...e,...t])]:pe(Object.create(null),$s(e),$s(t??{})):t}function rl(e,t){if(!e)return t;if(!t)return e;const n=pe(Object.create(null),e);for(const s in t)n[s]=ge(e[s],t[s]);return n}function Jr(){return{app:null,config:{isNativeTag:Ni,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let il=0;function ol(e,t){return function(s,r=null){V(s)||(s=pe({},s)),r!=null&&!ne(r)&&(r=null);const i=Jr(),l=new WeakSet,o=[];let c=!1;const d=i.app={_uid:il++,_component:s,_props:r,_container:null,_context:i,_instance:null,version:Ul,get config(){return i.config},set config(a){},use(a,...h){return l.has(a)||(a&&V(a.install)?(l.add(a),a.install(d,...h)):V(a)&&(l.add(a),a(d,...h))),d},mixin(a){return i.mixins.includes(a)||i.mixins.push(a),d},component(a,h){return h?(i.components[a]=h,d):i.components[a]},directive(a,h){return h?(i.directives[a]=h,d):i.directives[a]},mount(a,h,w){if(!c){const x=d._ceVNode||he(s,r);return x.appContext=i,w===!0?w="svg":w===!1&&(w=void 0),h&&t?t(x,a):e(x,a,w),c=!0,d._container=a,a.__vue_app__=d,Cs(x.component)}},onUnmount(a){o.push(a)},unmount(){c&&($e(o,d._instance,16),e(null,d._container),delete d._container.__vue_app__)},provide(a,h){return i.provides[a]=h,d},runWithContext(a){const h=it;it=d;try{return a()}finally{it=h}}};return d}}let it=null;function ll(e,t){if(oe){let n=oe.provides;const s=oe.parent&&oe.parent.provides;s===n&&(n=oe.provides=Object.create(s)),n[e]=t}}function ot(e,t,n=!1){const s=oe||xe;if(s||it){let r=it?it._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&V(t)?t.call(s&&s.proxy):t}}function Xr(){return!!(oe||xe||it)}const zr={},Yr=()=>Object.create(zr),Qr=e=>Object.getPrototypeOf(e)===zr;function cl(e,t,n,s=!1){const r={},i=Yr();e.propsDefaults=Object.create(null),Zr(e,t,r,i);for(const l in e.propsOptions[0])l in r||(r[l]=void 0);n?e.props=s?r:mo(r):e.type.props?e.props=r:e.props=i,e.attrs=i}function fl(e,t,n,s){const{props:r,attrs:i,vnode:{patchFlag:l}}=e,o=z(r),[c]=e.propsOptions;let d=!1;if((s||l>0)&&!(l&16)){if(l&8){const a=e.vnode.dynamicProps;for(let h=0;h<a.length;h++){let w=a[h];if(En(e.emitsOptions,w))continue;const x=t[w];if(c)if(Y(i,w))x!==i[w]&&(i[w]=x,d=!0);else{const O=Re(w);r[O]=ts(c,o,O,x,e,!1)}else x!==i[w]&&(i[w]=x,d=!0)}}}else{Zr(e,t,r,i)&&(d=!0);let a;for(const h in o)(!t||!Y(t,h)&&((a=ft(h))===h||!Y(t,a)))&&(c?n&&(n[h]!==void 0||n[a]!==void 0)&&(r[h]=ts(c,o,h,void 0,e,!0)):delete r[h]);if(i!==o)for(const h in i)(!t||!Y(t,h))&&(delete i[h],d=!0)}d&&Ve(e.attrs,"set","")}function Zr(e,t,n,s){const[r,i]=e.propsOptions;let l=!1,o;if(t)for(let c in t){if(mt(c))continue;const d=t[c];let a;r&&Y(r,a=Re(c))?!i||!i.includes(a)?n[a]=d:(o||(o={}))[a]=d:En(e.emitsOptions,c)||(!(c in s)||d!==s[c])&&(s[c]=d,l=!0)}if(i){const c=z(n),d=o||te;for(let a=0;a<i.length;a++){const h=i[a];n[h]=ts(r,c,h,d[h],e,!Y(d,h))}}return l}function ts(e,t,n,s,r,i){const l=e[n];if(l!=null){const o=Y(l,"default");if(o&&s===void 0){const c=l.default;if(l.type!==Function&&!l.skipFactory&&V(c)){const{propsDefaults:d}=r;if(n in d)s=d[n];else{const a=Vt(r);s=d[n]=c.call(null,t),a()}}else s=c;r.ce&&r.ce._setProp(n,s)}l[0]&&(i&&!o?s=!1:l[1]&&(s===""||s===ft(n))&&(s=!0))}return s}const al=new WeakMap;function ei(e,t,n=!1){const s=n?al:t.propsCache,r=s.get(e);if(r)return r;const i=e.props,l={},o=[];let c=!1;if(!V(e)){const a=h=>{c=!0;const[w,x]=ei(h,t,!0);pe(l,w),x&&o.push(...x)};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}if(!i&&!c)return ne(e)&&s.set(e,pt),pt;if($(i))for(let a=0;a<i.length;a++){const h=Re(i[a]);Ws(h)&&(l[h]=te)}else if(i)for(const a in i){const h=Re(a);if(Ws(h)){const w=i[a],x=l[h]=$(w)||V(w)?{type:w}:pe({},w),O=x.type;let R=!1,J=!0;if($(O))for(let j=0;j<O.length;++j){const K=O[j],p=V(K)&&K.name;if(p==="Boolean"){R=!0;break}else p==="String"&&(J=!1)}else R=V(O)&&O.name==="Boolean";x[0]=R,x[1]=J,(R||Y(x,"default"))&&o.push(h)}}const d=[l,o];return ne(e)&&s.set(e,d),d}function Ws(e){return e[0]!=="$"&&!mt(e)}const ws=e=>e[0]==="_"||e==="$stable",xs=e=>$(e)?e.map(Ce):[Ce(e)],ul=(e,t,n)=>{if(t._n)return t;const s=Io((...r)=>xs(t(...r)),n);return s._c=!1,s},ti=(e,t,n)=>{const s=e._ctx;for(const r in e){if(ws(r))continue;const i=e[r];if(V(i))t[r]=ul(r,i,s);else if(i!=null){const l=xs(i);t[r]=()=>l}}},ni=(e,t)=>{const n=xs(t);e.slots.default=()=>n},si=(e,t,n)=>{for(const s in t)(n||!ws(s))&&(e[s]=t[s])},dl=(e,t,n)=>{const s=e.slots=Yr();if(e.vnode.shapeFlag&32){const r=t.__;r&&Jn(s,"__",r,!0);const i=t._;i?(si(s,t,n),n&&Jn(s,"_",i,!0)):ti(t,s)}else t&&ni(e,t)},hl=(e,t,n)=>{const{vnode:s,slots:r}=e;let i=!0,l=te;if(s.shapeFlag&32){const o=t._;o?n&&o===1?i=!1:si(r,t,n):(i=!t.$stable,ti(t,r)),l=t}else t&&(ni(e,t),l={default:1});if(i)for(const o in r)!ws(o)&&l[o]==null&&delete r[o]},we=di;function pl(e){return gl(e,Ho)}function gl(e,t){const n=mn();n.__VUE__=!0;const{insert:s,remove:r,patchProp:i,createElement:l,createText:o,createComment:c,setText:d,setElementText:a,parentNode:h,nextSibling:w,setScopeId:x=je,insertStaticContent:O}=e,R=(f,u,m,y=null,_=null,b=null,T=void 0,E=null,S=!!u.dynamicChildren)=>{if(f===u)return;f&&!Tt(f,u)&&(y=Bt(f),Ie(f,_,b,!0),f=null),u.patchFlag===-2&&(S=!1,u.dynamicChildren=null);const{type:v,ref:F,shapeFlag:C}=u;switch(v){case lt:J(f,u,m,y);break;case ke:j(f,u,m,y);break;case Mt:f==null&&K(u,m,y,T);break;case Me:D(f,u,m,y,_,b,T,E,S);break;default:C&1?A(f,u,m,y,_,b,T,E,S):C&6?W(f,u,m,y,_,b,T,E,S):(C&64||C&128)&&v.process(f,u,m,y,_,b,T,E,S,at)}F!=null&&_?yt(F,f&&f.ref,b,u||f,!u):F==null&&f&&f.ref!=null&&yt(f.ref,null,b,f,!0)},J=(f,u,m,y)=>{if(f==null)s(u.el=o(u.children),m,y);else{const _=u.el=f.el;u.children!==f.children&&d(_,u.children)}},j=(f,u,m,y)=>{f==null?s(u.el=c(u.children||""),m,y):u.el=f.el},K=(f,u,m,y)=>{[f.el,f.anchor]=O(f.children,u,m,y,f.el,f.anchor)},p=({el:f,anchor:u},m,y)=>{let _;for(;f&&f!==u;)_=w(f),s(f,m,y),f=_;s(u,m,y)},g=({el:f,anchor:u})=>{let m;for(;f&&f!==u;)m=w(f),r(f),f=m;r(u)},A=(f,u,m,y,_,b,T,E,S)=>{u.type==="svg"?T="svg":u.type==="math"&&(T="mathml"),f==null?B(u,m,y,_,b,T,E,S):k(f,u,_,b,T,E,S)},B=(f,u,m,y,_,b,T,E)=>{let S,v;const{props:F,shapeFlag:C,transition:P,dirs:H}=f;if(S=f.el=l(f.type,b,F&&F.is,F),C&8?a(S,f.children):C&16&&q(f.children,S,null,y,_,Nn(f,b),T,E),H&&Ne(f,null,y,"created"),U(S,f,f.scopeId,T,y),F){for(const Z in F)Z!=="value"&&!mt(Z)&&i(S,Z,null,F[Z],b,y);"value"in F&&i(S,"value",null,F.value,b),(v=F.onVnodeBeforeMount)&&Te(v,y,f)}H&&Ne(f,null,y,"beforeMount");const X=ri(_,P);X&&P.beforeEnter(S),s(S,u,m),((v=F&&F.onVnodeMounted)||X||H)&&we(()=>{v&&Te(v,y,f),X&&P.enter(S),H&&Ne(f,null,y,"mounted")},_)},U=(f,u,m,y,_)=>{if(m&&x(f,m),y)for(let b=0;b<y.length;b++)x(f,y[b]);if(_){let b=_.subTree;if(u===b||ui(b.type)&&(b.ssContent===u||b.ssFallback===u)){const T=_.vnode;U(f,T,T.scopeId,T.slotScopeIds,_.parent)}}},q=(f,u,m,y,_,b,T,E,S=0)=>{for(let v=S;v<f.length;v++){const F=f[v]=E?Ge(f[v]):Ce(f[v]);R(null,F,u,m,y,_,b,T,E)}},k=(f,u,m,y,_,b,T)=>{const E=u.el=f.el;let{patchFlag:S,dynamicChildren:v,dirs:F}=u;S|=f.patchFlag&16;const C=f.props||te,P=u.props||te;let H;if(m&&et(m,!1),(H=P.onVnodeBeforeUpdate)&&Te(H,m,u,f),F&&Ne(u,f,m,"beforeUpdate"),m&&et(m,!0),(C.innerHTML&&P.innerHTML==null||C.textContent&&P.textContent==null)&&a(E,""),v?L(f.dynamicChildren,v,E,m,y,Nn(u,_),b):T||I(f,u,E,null,m,y,Nn(u,_),b,!1),S>0){if(S&16)M(E,C,P,m,_);else if(S&2&&C.class!==P.class&&i(E,"class",null,P.class,_),S&4&&i(E,"style",C.style,P.style,_),S&8){const X=u.dynamicProps;for(let Z=0;Z<X.length;Z++){const Q=X[Z],be=C[Q],fe=P[Q];(fe!==be||Q==="value")&&i(E,Q,be,fe,_,m)}}S&1&&f.children!==u.children&&a(E,u.children)}else!T&&v==null&&M(E,C,P,m,_);((H=P.onVnodeUpdated)||F)&&we(()=>{H&&Te(H,m,u,f),F&&Ne(u,f,m,"updated")},y)},L=(f,u,m,y,_,b,T)=>{for(let E=0;E<u.length;E++){const S=f[E],v=u[E],F=S.el&&(S.type===Me||!Tt(S,v)||S.shapeFlag&198)?h(S.el):m;R(S,v,F,null,y,_,b,T,!0)}},M=(f,u,m,y,_)=>{if(u!==m){if(u!==te)for(const b in u)!mt(b)&&!(b in m)&&i(f,b,u[b],null,_,y);for(const b in m){if(mt(b))continue;const T=m[b],E=u[b];T!==E&&b!=="value"&&i(f,b,E,T,_,y)}"value"in m&&i(f,"value",u.value,m.value,_)}},D=(f,u,m,y,_,b,T,E,S)=>{const v=u.el=f?f.el:o(""),F=u.anchor=f?f.anchor:o("");let{patchFlag:C,dynamicChildren:P,slotScopeIds:H}=u;H&&(E=E?E.concat(H):H),f==null?(s(v,m,y),s(F,m,y),q(u.children||[],m,F,_,b,T,E,S)):C>0&&C&64&&P&&f.dynamicChildren?(L(f.dynamicChildren,P,m,_,b,T,E),(u.key!=null||_&&u===_.subTree)&&ii(f,u,!0)):I(f,u,m,F,_,b,T,E,S)},W=(f,u,m,y,_,b,T,E,S)=>{u.slotScopeIds=E,f==null?u.shapeFlag&512?_.ctx.activate(u,m,y,T,S):Ee(u,m,y,_,b,T,S):_e(f,u,S)},Ee=(f,u,m,y,_,b,T)=>{const E=f.component=Fl(f,y,_);if(kr(f)&&(E.ctx.renderer=at),Ll(E,!1,T),E.asyncDep){if(_&&_.registerDep(E,N,T),!f.el){const S=E.subTree=he(ke);j(null,S,u,m)}}else N(E,f,u,m,_,b,T)},_e=(f,u,m)=>{const y=u.component=f.component;if(El(f,u,m))if(y.asyncDep&&!y.asyncResolved){G(y,u,m);return}else y.next=u,y.update();else u.el=f.el,y.vnode=u},N=(f,u,m,y,_,b,T)=>{const E=()=>{if(f.isMounted){let{next:C,bu:P,u:H,parent:X,vnode:Z}=f;{const ye=oi(f);if(ye){C&&(C.el=Z.el,G(f,C,T)),ye.asyncDep.then(()=>{f.isUnmounted||E()});return}}let Q=C,be;et(f,!1),C?(C.el=Z.el,G(f,C,T)):C=Z,P&&On(P),(be=C.props&&C.props.onVnodeBeforeUpdate)&&Te(be,X,C,Z),et(f,!0);const fe=jn(f),Oe=f.subTree;f.subTree=fe,R(Oe,fe,h(Oe.el),Bt(Oe),f,_,b),C.el=fe.el,Q===null&&ai(f,fe.el),H&&we(H,_),(be=C.props&&C.props.onVnodeUpdated)&&we(()=>Te(be,X,C,Z),_)}else{let C;const{el:P,props:H}=u,{bm:X,m:Z,parent:Q,root:be,type:fe}=f,Oe=vt(u);if(et(f,!1),X&&On(X),!Oe&&(C=H&&H.onVnodeBeforeMount)&&Te(C,Q,u),et(f,!0),P&&An){const ye=()=>{f.subTree=jn(f),An(P,f.subTree,f,_,null)};Oe&&fe.__asyncHydrate?fe.__asyncHydrate(P,f,ye):ye()}else{be.ce&&be.ce._def.shadowRoot!==!1&&be.ce._injectChildStyle(fe);const ye=f.subTree=jn(f);R(null,ye,m,y,f,_,b),u.el=ye.el}if(Z&&we(Z,_),!Oe&&(C=H&&H.onVnodeMounted)){const ye=u;we(()=>Te(C,Q,ye),_)}(u.shapeFlag&256||Q&&vt(Q.vnode)&&Q.vnode.shapeFlag&256)&&f.a&&we(f.a,_),f.isMounted=!0,u=m=y=null}};f.scope.on();const S=f.effect=new xr(E);f.scope.off();const v=f.update=S.run.bind(S),F=f.job=S.runIfDirty.bind(S);F.i=f,F.id=f.uid,S.scheduler=()=>bs(F),et(f,!0),v()},G=(f,u,m)=>{u.component=f;const y=f.vnode.props;f.vnode=u,f.next=null,fl(f,u.props,y,m),hl(f,u.children,m),We(),Ls(f),Be()},I=(f,u,m,y,_,b,T,E,S=!1)=>{const v=f&&f.children,F=f?f.shapeFlag:0,C=u.children,{patchFlag:P,shapeFlag:H}=u;if(P>0){if(P&128){Wt(v,C,m,y,_,b,T,E,S);return}else if(P&256){re(v,C,m,y,_,b,T,E,S);return}}H&8?(F&16&&xt(v,_,b),C!==v&&a(m,C)):F&16?H&16?Wt(v,C,m,y,_,b,T,E,S):xt(v,_,b,!0):(F&8&&a(m,""),H&16&&q(C,m,y,_,b,T,E,S))},re=(f,u,m,y,_,b,T,E,S)=>{f=f||pt,u=u||pt;const v=f.length,F=u.length,C=Math.min(v,F);let P;for(P=0;P<C;P++){const H=u[P]=S?Ge(u[P]):Ce(u[P]);R(f[P],H,m,null,_,b,T,E,S)}v>F?xt(f,_,b,!0,!1,C):q(u,m,y,_,b,T,E,S,C)},Wt=(f,u,m,y,_,b,T,E,S)=>{let v=0;const F=u.length;let C=f.length-1,P=F-1;for(;v<=C&&v<=P;){const H=f[v],X=u[v]=S?Ge(u[v]):Ce(u[v]);if(Tt(H,X))R(H,X,m,null,_,b,T,E,S);else break;v++}for(;v<=C&&v<=P;){const H=f[C],X=u[P]=S?Ge(u[P]):Ce(u[P]);if(Tt(H,X))R(H,X,m,null,_,b,T,E,S);else break;C--,P--}if(v>C){if(v<=P){const H=P+1,X=H<F?u[H].el:y;for(;v<=P;)R(null,u[v]=S?Ge(u[v]):Ce(u[v]),m,X,_,b,T,E,S),v++}}else if(v>P)for(;v<=C;)Ie(f[v],_,b,!0),v++;else{const H=v,X=v,Z=new Map;for(v=X;v<=P;v++){const ve=u[v]=S?Ge(u[v]):Ce(u[v]);ve.key!=null&&Z.set(ve.key,v)}let Q,be=0;const fe=P-X+1;let Oe=!1,ye=0;const St=new Array(fe);for(v=0;v<fe;v++)St[v]=0;for(v=H;v<=C;v++){const ve=f[v];if(be>=fe){Ie(ve,_,b,!0);continue}let Fe;if(ve.key!=null)Fe=Z.get(ve.key);else for(Q=X;Q<=P;Q++)if(St[Q-X]===0&&Tt(ve,u[Q])){Fe=Q;break}Fe===void 0?Ie(ve,_,b,!0):(St[Fe-X]=v+1,Fe>=ye?ye=Fe:Oe=!0,R(ve,u[Fe],m,null,_,b,T,E,S),be++)}const Os=Oe?ml(St):pt;for(Q=Os.length-1,v=fe-1;v>=0;v--){const ve=X+v,Fe=u[ve],Ms=ve+1<F?u[ve+1].el:y;St[v]===0?R(null,Fe,m,Ms,_,b,T,E,S):Oe&&(Q<0||v!==Os[Q]?Ze(Fe,m,Ms,2):Q--)}}},Ze=(f,u,m,y,_=null)=>{const{el:b,type:T,transition:E,children:S,shapeFlag:v}=f;if(v&6){Ze(f.component.subTree,u,m,y);return}if(v&128){f.suspense.move(u,m,y);return}if(v&64){T.move(f,u,m,at);return}if(T===Me){s(b,u,m);for(let C=0;C<S.length;C++)Ze(S[C],u,m,y);s(f.anchor,u,m);return}if(T===Mt){p(f,u,m);return}if(y!==2&&v&1&&E)if(y===0)E.beforeEnter(b),s(b,u,m),we(()=>E.enter(b),_);else{const{leave:C,delayLeave:P,afterLeave:H}=E,X=()=>{f.ctx.isUnmounted?r(b):s(b,u,m)},Z=()=>{C(b,()=>{X(),H&&H()})};P?P(b,X,Z):Z()}else s(b,u,m)},Ie=(f,u,m,y=!1,_=!1)=>{const{type:b,props:T,ref:E,children:S,dynamicChildren:v,shapeFlag:F,patchFlag:C,dirs:P,cacheIndex:H}=f;if(C===-2&&(_=!1),E!=null&&(We(),yt(E,null,m,f,!0),Be()),H!=null&&(u.renderCache[H]=void 0),F&256){u.ctx.deactivate(f);return}const X=F&1&&P,Z=!vt(f);let Q;if(Z&&(Q=T&&T.onVnodeBeforeUnmount)&&Te(Q,u,f),F&6)Li(f.component,m,y);else{if(F&128){f.suspense.unmount(m,y);return}X&&Ne(f,null,u,"beforeUnmount"),F&64?f.type.remove(f,u,m,at,y):v&&!v.hasOnce&&(b!==Me||C>0&&C&64)?xt(v,u,m,!1,!0):(b===Me&&C&384||!_&&F&16)&&xt(S,u,m),y&&As(f)}(Z&&(Q=T&&T.onVnodeUnmounted)||X)&&we(()=>{Q&&Te(Q,u,f),X&&Ne(f,null,u,"unmounted")},m)},As=f=>{const{type:u,el:m,anchor:y,transition:_}=f;if(u===Me){Fi(m,y);return}if(u===Mt){g(f);return}const b=()=>{r(m),_&&!_.persisted&&_.afterLeave&&_.afterLeave()};if(f.shapeFlag&1&&_&&!_.persisted){const{leave:T,delayLeave:E}=_,S=()=>T(m,b);E?E(f.el,b,S):S()}else b()},Fi=(f,u)=>{let m;for(;f!==u;)m=w(f),r(f),f=m;r(u)},Li=(f,u,m)=>{const{bum:y,scope:_,job:b,subTree:T,um:E,m:S,a:v,parent:F,slots:{__:C}}=f;Bs(S),Bs(v),y&&On(y),F&&$(C)&&C.forEach(P=>{F.renderCache[P]=void 0}),_.stop(),b&&(b.flags|=8,Ie(T,f,u,m)),E&&we(E,u),we(()=>{f.isUnmounted=!0},u),u&&u.pendingBranch&&!u.isUnmounted&&f.asyncDep&&!f.asyncResolved&&f.suspenseId===u.pendingId&&(u.deps--,u.deps===0&&u.resolve())},xt=(f,u,m,y=!1,_=!1,b=0)=>{for(let T=b;T<f.length;T++)Ie(f[T],u,m,y,_)},Bt=f=>{if(f.shapeFlag&6)return Bt(f.component.subTree);if(f.shapeFlag&128)return f.suspense.next();const u=w(f.anchor||f.el),m=u&&u[Fo];return m?w(m):u};let Tn=!1;const Rs=(f,u,m)=>{f==null?u._vnode&&Ie(u._vnode,null,null,!0):R(u._vnode||null,f,u,null,null,null,m),u._vnode=f,Tn||(Tn=!0,Ls(),cn(),Tn=!1)},at={p:R,um:Ie,m:Ze,r:As,mt:Ee,mc:q,pc:I,pbc:L,n:Bt,o:e};let Cn,An;return t&&([Cn,An]=t(at)),{render:Rs,hydrate:Cn,createApp:ol(Rs,Cn)}}function Nn({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function et({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function ri(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function ii(e,t,n=!1){const s=e.children,r=t.children;if($(s)&&$(r))for(let i=0;i<s.length;i++){const l=s[i];let o=r[i];o.shapeFlag&1&&!o.dynamicChildren&&((o.patchFlag<=0||o.patchFlag===32)&&(o=r[i]=Ge(r[i]),o.el=l.el),!n&&o.patchFlag!==-2&&ii(l,o)),o.type===lt&&(o.el=l.el),o.type===ke&&!o.el&&(o.el=l.el)}}function ml(e){const t=e.slice(),n=[0];let s,r,i,l,o;const c=e.length;for(s=0;s<c;s++){const d=e[s];if(d!==0){if(r=n[n.length-1],e[r]<d){t[s]=r,n.push(s);continue}for(i=0,l=n.length-1;i<l;)o=i+l>>1,e[n[o]]<d?i=o+1:l=o;d<e[n[i]]&&(i>0&&(t[s]=n[i-1]),n[i]=s)}}for(i=n.length,l=n[i-1];i-- >0;)n[i]=l,l=t[l];return n}function oi(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:oi(t)}function Bs(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const _l=Symbol.for("v-scx"),bl=()=>ot(_l);function li(e,t){return Ss(e,null,t)}function He(e,t,n){return Ss(e,t,n)}function Ss(e,t,n=te){const{immediate:s,deep:r,flush:i,once:l}=n,o=pe({},n),c=t&&s||!t&&i!=="post";let d;if(jt){if(i==="sync"){const x=bl();d=x.__watcherHandles||(x.__watcherHandles=[])}else if(!c){const x=()=>{};return x.stop=je,x.resume=je,x.pause=je,x}}const a=oe;o.call=(x,O,R)=>$e(x,a,O,R);let h=!1;i==="post"?o.scheduler=x=>{we(x,a&&a.suspense)}:i!=="sync"&&(h=!0,o.scheduler=(x,O)=>{O?x():bs(x)}),o.augmentJob=x=>{t&&(x.flags|=4),h&&(x.flags|=2,a&&(x.id=a.uid,x.i=a))};const w=Ro(e,t,o);return jt&&(d?d.push(w):c&&w()),w}function yl(e,t,n){const s=this.proxy,r=ie(e)?e.includes(".")?ci(s,e):()=>s[e]:e.bind(s,s);let i;V(t)?i=t:(i=t.handler,n=t);const l=Vt(this),o=Ss(r,i.bind(s),n);return l(),o}function ci(e,t){const n=t.split(".");return()=>{let s=e;for(let r=0;r<n.length&&s;r++)s=s[n[r]];return s}}const vl=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Re(t)}Modifiers`]||e[`${ft(t)}Modifiers`];function wl(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||te;let r=n;const i=t.startsWith("update:"),l=i&&vl(s,t.slice(7));l&&(l.trim&&(r=n.map(a=>ie(a)?a.trim():a)),l.number&&(r=n.map(Ui)));let o,c=s[o=Rn(t)]||s[o=Rn(Re(t))];!c&&i&&(c=s[o=Rn(ft(t))]),c&&$e(c,e,6,r);const d=s[o+"Once"];if(d){if(!e.emitted)e.emitted={};else if(e.emitted[o])return;e.emitted[o]=!0,$e(d,e,6,r)}}function fi(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(r!==void 0)return r;const i=e.emits;let l={},o=!1;if(!V(e)){const c=d=>{const a=fi(d,t,!0);a&&(o=!0,pe(l,a))};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!i&&!o?(ne(e)&&s.set(e,null),null):($(i)?i.forEach(c=>l[c]=null):pe(l,i),ne(e)&&s.set(e,l),l)}function En(e,t){return!e||!$t(t)?!1:(t=t.slice(2).replace(/Once$/,""),Y(e,t[0].toLowerCase()+t.slice(1))||Y(e,ft(t))||Y(e,t))}function jn(e){const{type:t,vnode:n,proxy:s,withProxy:r,propsOptions:[i],slots:l,attrs:o,emit:c,render:d,renderCache:a,props:h,data:w,setupState:x,ctx:O,inheritAttrs:R}=e,J=fn(e);let j,K;try{if(n.shapeFlag&4){const g=r||s,A=g;j=Ce(d.call(A,g,a,h,x,w,O)),K=o}else{const g=t;j=Ce(g.length>1?g(h,{attrs:o,slots:l,emit:c}):g(h,null)),K=t.props?o:xl(o)}}catch(g){Pt.length=0,wn(g,e,1),j=he(ke)}let p=j;if(K&&R!==!1){const g=Object.keys(K),{shapeFlag:A}=p;g.length&&A&7&&(i&&g.some(os)&&(K=Sl(K,i)),p=wt(p,K,!1,!0))}return n.dirs&&(p=wt(p,null,!1,!0),p.dirs=p.dirs?p.dirs.concat(n.dirs):n.dirs),n.transition&&ys(p,n.transition),j=p,fn(J),j}const xl=e=>{let t;for(const n in e)(n==="class"||n==="style"||$t(n))&&((t||(t={}))[n]=e[n]);return t},Sl=(e,t)=>{const n={};for(const s in e)(!os(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function El(e,t,n){const{props:s,children:r,component:i}=e,{props:l,children:o,patchFlag:c}=t,d=i.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&c>=0){if(c&1024)return!0;if(c&16)return s?ks(s,l,d):!!l;if(c&8){const a=t.dynamicProps;for(let h=0;h<a.length;h++){const w=a[h];if(l[w]!==s[w]&&!En(d,w))return!0}}}else return(r||o)&&(!o||!o.$stable)?!0:s===l?!1:s?l?ks(s,l,d):!0:!!l;return!1}function ks(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const i=s[r];if(t[i]!==e[i]&&!En(n,i))return!0}return!1}function ai({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const ui=e=>e.__isSuspense;function di(e,t){t&&t.pendingBranch?$(e)?t.effects.push(...e):t.effects.push(e):Po(e)}const Me=Symbol.for("v-fgt"),lt=Symbol.for("v-txt"),ke=Symbol.for("v-cmt"),Mt=Symbol.for("v-stc"),Pt=[];let Se=null;function Tl(e=!1){Pt.push(Se=e?null:[])}function Cl(){Pt.pop(),Se=Pt[Pt.length-1]||null}let Nt=1;function Ks(e,t=!1){Nt+=e,e<0&&Se&&t&&(Se.hasOnce=!0)}function hi(e){return e.dynamicChildren=Nt>0?Se||pt:null,Cl(),Nt>0&&Se&&Se.push(e),e}function pf(e,t,n,s,r,i){return hi(gi(e,t,n,s,r,i,!0))}function Al(e,t,n,s,r){return hi(he(e,t,n,s,r,!0))}function un(e){return e?e.__v_isVNode===!0:!1}function Tt(e,t){return e.type===t.type&&e.key===t.key}const pi=({key:e})=>e??null,tn=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?ie(e)||le(e)||V(e)?{i:xe,r:e,k:t,f:!!n}:e:null);function gi(e,t=null,n=null,s=0,r=null,i=e===Me?0:1,l=!1,o=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&pi(t),ref:t&&tn(t),scopeId:Vr,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:xe};return o?(Es(c,n),i&128&&e.normalize(c)):n&&(c.shapeFlag|=ie(n)?8:16),Nt>0&&!l&&Se&&(c.patchFlag>0||i&6)&&c.patchFlag!==32&&Se.push(c),c}const he=Rl;function Rl(e,t=null,n=null,s=0,r=null,i=!1){if((!e||e===Yo)&&(e=ke),un(e)){const o=wt(e,t,!0);return n&&Es(o,n),Nt>0&&!i&&Se&&(o.shapeFlag&6?Se[Se.indexOf(e)]=o:Se.push(o)),o.patchFlag=-2,o}if(Dl(e)&&(e=e.__vccOpts),t){t=Ol(t);let{class:o,style:c}=t;o&&!ie(o)&&(t.class=as(o)),ne(c)&&(ms(c)&&!$(c)&&(c=pe({},c)),t.style=fs(c))}const l=ie(e)?1:ui(e)?128:Lo(e)?64:ne(e)?4:V(e)?2:0;return gi(e,t,n,s,r,l,i,!0)}function Ol(e){return e?ms(e)||Qr(e)?pe({},e):e:null}function wt(e,t,n=!1,s=!1){const{props:r,ref:i,patchFlag:l,children:o,transition:c}=e,d=t?Ml(r||{},t):r,a={__v_isVNode:!0,__v_skip:!0,type:e.type,props:d,key:d&&pi(d),ref:t&&t.ref?n&&i?$(i)?i.concat(tn(t)):[i,tn(t)]:tn(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:o,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Me?l===-1?16:l|16:l,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&wt(e.ssContent),ssFallback:e.ssFallback&&wt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&s&&ys(a,c.clone(a)),a}function mi(e=" ",t=0){return he(lt,null,e,t)}function gf(e,t){const n=he(Mt,null,e);return n.staticCount=t,n}function mf(e="",t=!1){return t?(Tl(),Al(ke,null,e)):he(ke,null,e)}function Ce(e){return e==null||typeof e=="boolean"?he(ke):$(e)?he(Me,null,e.slice()):un(e)?Ge(e):he(lt,null,String(e))}function Ge(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:wt(e)}function Es(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if($(t))n=16;else if(typeof t=="object")if(s&65){const r=t.default;r&&(r._c&&(r._d=!1),Es(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!Qr(t)?t._ctx=xe:r===3&&xe&&(xe.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else V(t)?(t={default:t,_ctx:xe},n=32):(t=String(t),s&64?(n=16,t=[mi(t)]):n=8);e.children=t,e.shapeFlag|=n}function Ml(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const r in s)if(r==="class")t.class!==s.class&&(t.class=as([t.class,s.class]));else if(r==="style")t.style=fs([t.style,s.style]);else if($t(r)){const i=t[r],l=s[r];l&&i!==l&&!($(i)&&i.includes(l))&&(t[r]=i?[].concat(i,l):l)}else r!==""&&(t[r]=s[r])}return t}function Te(e,t,n,s=null){$e(e,t,7,[n,s])}const Pl=Jr();let Il=0;function Fl(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||Pl,i={uid:Il++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Ji(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:ei(s,r),emitsOptions:fi(s,r),emit:null,emitted:null,propsDefaults:te,inheritAttrs:s.inheritAttrs,ctx:te,data:te,props:te,attrs:te,slots:te,refs:te,setupState:te,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=wl.bind(null,i),e.ce&&e.ce(i),i}let oe=null;const Ts=()=>oe||xe;let dn,ns;{const e=mn(),t=(n,s)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(s),i=>{r.length>1?r.forEach(l=>l(i)):r[0](i)}};dn=t("__VUE_INSTANCE_SETTERS__",n=>oe=n),ns=t("__VUE_SSR_SETTERS__",n=>jt=n)}const Vt=e=>{const t=oe;return dn(e),e.scope.on(),()=>{e.scope.off(),dn(t)}},qs=()=>{oe&&oe.scope.off(),dn(null)};function _i(e){return e.vnode.shapeFlag&4}let jt=!1;function Ll(e,t=!1,n=!1){t&&ns(t);const{props:s,children:r}=e.vnode,i=_i(e);cl(e,s,i,t),dl(e,r,n||t);const l=i?Nl(e,t):void 0;return t&&ns(!1),l}function Nl(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Zo);const{setup:s}=n;if(s){We();const r=e.setupContext=s.length>1?Hl(e):null,i=Vt(e),l=Dt(s,e,0,[e.props,r]),o=gr(l);if(Be(),i(),(o||e.sp)&&!vt(e)&&Br(e),o){if(l.then(qs,qs),t)return l.then(c=>{Gs(e,c)}).catch(c=>{wn(c,e,0)});e.asyncDep=l}else Gs(e,l)}else bi(e)}function Gs(e,t,n){V(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ne(t)&&(e.setupState=Hr(t)),bi(e)}function bi(e,t,n){const s=e.type;e.render||(e.render=s.render||je);{const r=Vt(e);We();try{el(e)}finally{Be(),r()}}}const jl={get(e,t){return de(e,"get",""),e[t]}};function Hl(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,jl),slots:e.slots,emit:e.emit,expose:t}}function Cs(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Hr(en(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Ot)return Ot[n](e)},has(t,n){return n in t||n in Ot}})):e.proxy}function $l(e,t=!0){return V(e)?e.displayName||e.name:e.name||t&&e.__name}function Dl(e){return V(e)&&"__vccOpts"in e}const se=(e,t)=>Co(e,t,jt);function Js(e,t,n){const s=arguments.length;return s===2?ne(t)&&!$(t)?un(t)?he(e,null,[t]):he(e,t):he(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&un(n)&&(n=[n]),he(e,t,n))}const Ul="3.5.17";/**
* @vue/runtime-dom v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ss;const Xs=typeof window<"u"&&window.trustedTypes;if(Xs)try{ss=Xs.createPolicy("vue",{createHTML:e=>e})}catch{}const yi=ss?e=>ss.createHTML(e):e=>e,Vl="http://www.w3.org/2000/svg",Wl="http://www.w3.org/1998/Math/MathML",Ue=typeof document<"u"?document:null,zs=Ue&&Ue.createElement("template"),Bl={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r=t==="svg"?Ue.createElementNS(Vl,e):t==="mathml"?Ue.createElementNS(Wl,e):n?Ue.createElement(e,{is:n}):Ue.createElement(e);return e==="select"&&s&&s.multiple!=null&&r.setAttribute("multiple",s.multiple),r},createText:e=>Ue.createTextNode(e),createComment:e=>Ue.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Ue.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,i){const l=n?n.previousSibling:t.lastChild;if(r&&(r===i||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===i||!(r=r.nextSibling)););else{zs.innerHTML=yi(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const o=zs.content;if(s==="svg"||s==="mathml"){const c=o.firstChild;for(;c.firstChild;)o.appendChild(c.firstChild);o.removeChild(c)}t.insertBefore(o,n)}return[l?l.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},kl=Symbol("_vtc");function Kl(e,t,n){const s=e[kl];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Ys=Symbol("_vod"),ql=Symbol("_vsh"),Gl=Symbol(""),Jl=/(^|;)\s*display\s*:/;function Xl(e,t,n){const s=e.style,r=ie(n);let i=!1;if(n&&!r){if(t)if(ie(t))for(const l of t.split(";")){const o=l.slice(0,l.indexOf(":")).trim();n[o]==null&&nn(s,o,"")}else for(const l in t)n[l]==null&&nn(s,l,"");for(const l in n)l==="display"&&(i=!0),nn(s,l,n[l])}else if(r){if(t!==n){const l=s[Gl];l&&(n+=";"+l),s.cssText=n,i=Jl.test(n)}}else t&&e.removeAttribute("style");Ys in e&&(e[Ys]=i?s.display:"",e[ql]&&(s.display="none"))}const Qs=/\s*!important$/;function nn(e,t,n){if($(n))n.forEach(s=>nn(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=zl(e,t);Qs.test(n)?e.setProperty(ft(s),n.replace(Qs,""),"important"):e[s]=n}}const Zs=["Webkit","Moz","ms"],Hn={};function zl(e,t){const n=Hn[t];if(n)return n;let s=Re(t);if(s!=="filter"&&s in e)return Hn[t]=s;s=gn(s);for(let r=0;r<Zs.length;r++){const i=Zs[r]+s;if(i in e)return Hn[t]=i}return t}const er="http://www.w3.org/1999/xlink";function tr(e,t,n,s,r,i=qi(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(er,t.slice(6,t.length)):e.setAttributeNS(er,t,n):n==null||i&&!br(n)?e.removeAttribute(t):e.setAttribute(t,i?"":Qe(n)?String(n):n)}function nr(e,t,n,s,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?yi(n):n);return}const i=e.tagName;if(t==="value"&&i!=="PROGRESS"&&!i.includes("-")){const o=i==="OPTION"?e.getAttribute("value")||"":e.value,c=n==null?e.type==="checkbox"?"on":"":String(n);(o!==c||!("_value"in e))&&(e.value=c),n==null&&e.removeAttribute(t),e._value=n;return}let l=!1;if(n===""||n==null){const o=typeof e[t];o==="boolean"?n=br(n):n==null&&o==="string"?(n="",l=!0):o==="number"&&(n=0,l=!0)}try{e[t]=n}catch{}l&&e.removeAttribute(r||t)}function Yl(e,t,n,s){e.addEventListener(t,n,s)}function Ql(e,t,n,s){e.removeEventListener(t,n,s)}const sr=Symbol("_vei");function Zl(e,t,n,s,r=null){const i=e[sr]||(e[sr]={}),l=i[t];if(s&&l)l.value=s;else{const[o,c]=ec(t);if(s){const d=i[t]=sc(s,r);Yl(e,o,d,c)}else l&&(Ql(e,o,l,c),i[t]=void 0)}}const rr=/(?:Once|Passive|Capture)$/;function ec(e){let t;if(rr.test(e)){t={};let s;for(;s=e.match(rr);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):ft(e.slice(2)),t]}let $n=0;const tc=Promise.resolve(),nc=()=>$n||(tc.then(()=>$n=0),$n=Date.now());function sc(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;$e(rc(s,n.value),t,5,[s])};return n.value=e,n.attached=nc(),n}function rc(e,t){if($(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>r=>!r._stopped&&s&&s(r))}else return t}const ir=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,ic=(e,t,n,s,r,i)=>{const l=r==="svg";t==="class"?Kl(e,s,l):t==="style"?Xl(e,n,s):$t(t)?os(t)||Zl(e,t,n,s,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):oc(e,t,s,l))?(nr(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&tr(e,t,s,l,i,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!ie(s))?nr(e,Re(t),s,i,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),tr(e,t,s,l))};function oc(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&ir(t)&&V(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return ir(t)&&ie(n)?!1:t in e}const lc=pe({patchProp:ic},Bl);let Dn,or=!1;function cc(){return Dn=or?Dn:pl(lc),or=!0,Dn}const _f=(...e)=>{const t=cc().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=ac(s);if(r)return n(r,!0,fc(r))},t};function fc(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function ac(e){return ie(e)?document.querySelector(e):e}const uc=window.__VP_SITE_DATA__;function dc(e){return wr()?(Xi(e),!0):!1}const Un=new WeakMap,hc=(...e)=>{var t;const n=e[0],s=(t=Ts())==null?void 0:t.proxy;if(s==null&&!Xr())throw new Error("injectLocal must be called in setup");return s&&Un.has(s)&&n in Un.get(s)?Un.get(s)[n]:ot(...e)},pc=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const gc=Object.prototype.toString,mc=e=>gc.call(e)==="[object Object]",_c=()=>{};function bc(e,t){function n(...s){return new Promise((r,i)=>{Promise.resolve(e(()=>t.apply(this,s),{fn:t,thisArg:this,args:s})).then(r).catch(i)})}return n}const vi=e=>e();function yc(e=vi,t={}){const{initialState:n="active"}=t,s=wi(n==="active");function r(){s.value=!1}function i(){s.value=!0}const l=(...o)=>{s.value&&e(...o)};return{isActive:vn(s),pause:r,resume:i,eventFilter:l}}function lr(e){return e.endsWith("rem")?Number.parseFloat(e)*16:Number.parseFloat(e)}function vc(e){return Ts()}function Vn(e){return Array.isArray(e)?e:[e]}function wi(...e){if(e.length!==1)return So(...e);const t=e[0];return typeof t=="function"?vn(vo(()=>({get:t,set:_c}))):rt(t)}function wc(e,t,n={}){const{eventFilter:s=vi,...r}=n;return He(e,bc(s,t),r)}function xc(e,t,n={}){const{eventFilter:s,initialState:r="active",...i}=n,{eventFilter:l,pause:o,resume:c,isActive:d}=yc(s,{initialState:r});return{stop:wc(e,t,{...i,eventFilter:l}),pause:o,resume:c,isActive:d}}function xi(e,t=!0,n){vc()?Ut(e,n):t?e():xn(e)}function Sc(e,t,n){return He(e,t,{...n,immediate:!0})}const Ht=pc?window:void 0;function Si(e){var t;const n=ze(e);return(t=n==null?void 0:n.$el)!=null?t:n}function rs(...e){const t=[],n=()=>{t.forEach(o=>o()),t.length=0},s=(o,c,d,a)=>(o.addEventListener(c,d,a),()=>o.removeEventListener(c,d,a)),r=se(()=>{const o=Vn(ze(e[0])).filter(c=>c!=null);return o.every(c=>typeof c!="string")?o:void 0}),i=Sc(()=>{var o,c;return[(c=(o=r.value)==null?void 0:o.map(d=>Si(d)))!=null?c:[Ht].filter(d=>d!=null),Vn(ze(r.value?e[1]:e[0])),Vn(_s(r.value?e[2]:e[1])),ze(r.value?e[3]:e[2])]},([o,c,d,a])=>{if(n(),!(o!=null&&o.length)||!(c!=null&&c.length)||!(d!=null&&d.length))return;const h=mc(a)?{...a}:a;t.push(...o.flatMap(w=>c.flatMap(x=>d.map(O=>s(w,x,O,h)))))},{flush:"post"}),l=()=>{i(),n()};return dc(n),l}function Ec(){const e=_t(!1),t=Ts();return t&&Ut(()=>{e.value=!0},t),e}function Tc(e){const t=Ec();return se(()=>(t.value,!!e()))}const Cc=Symbol("vueuse-ssr-width");function Ac(){const e=Xr()?hc(Cc,null):null;return typeof e=="number"?e:void 0}function Rc(e,t={}){const{window:n=Ht,ssrWidth:s=Ac()}=t,r=Tc(()=>n&&"matchMedia"in n&&typeof n.matchMedia=="function"),i=_t(typeof s=="number"),l=_t(),o=_t(!1),c=d=>{o.value=d.matches};return li(()=>{if(i.value){i.value=!r.value;const d=ze(e).split(",");o.value=d.some(a=>{const h=a.includes("not all"),w=a.match(/\(\s*min-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/),x=a.match(/\(\s*max-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/);let O=!!(w||x);return w&&O&&(O=s>=lr(w[1])),x&&O&&(O=s<=lr(x[1])),h?!O:O});return}r.value&&(l.value=n.matchMedia(ze(e)),o.value=l.value.matches)}),rs(l,"change",c,{passive:!0}),se(()=>o.value)}const zt=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},Yt="__vueuse_ssr_handlers__",Oc=Mc();function Mc(){return Yt in zt||(zt[Yt]=zt[Yt]||{}),zt[Yt]}function Ei(e,t){return Oc[e]||t}function Ti(e){return Rc("(prefers-color-scheme: dark)",e)}function Pc(e){return e==null?"any":e instanceof Set?"set":e instanceof Map?"map":e instanceof Date?"date":typeof e=="boolean"?"boolean":typeof e=="string"?"string":typeof e=="object"?"object":Number.isNaN(e)?"any":"number"}const Ic={boolean:{read:e=>e==="true",write:e=>String(e)},object:{read:e=>JSON.parse(e),write:e=>JSON.stringify(e)},number:{read:e=>Number.parseFloat(e),write:e=>String(e)},any:{read:e=>e,write:e=>String(e)},string:{read:e=>e,write:e=>String(e)},map:{read:e=>new Map(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e.entries()))},set:{read:e=>new Set(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e))},date:{read:e=>new Date(e),write:e=>e.toISOString()}},cr="vueuse-storage";function Fc(e,t,n,s={}){var r;const{flush:i="pre",deep:l=!0,listenToStorageChanges:o=!0,writeDefaults:c=!0,mergeDefaults:d=!1,shallow:a,window:h=Ht,eventFilter:w,onError:x=M=>{console.error(M)},initOnMounted:O}=s,R=(a?_t:rt)(typeof t=="function"?t():t),J=se(()=>ze(e));if(!n)try{n=Ei("getDefaultStorage",()=>{var M;return(M=Ht)==null?void 0:M.localStorage})()}catch(M){x(M)}if(!n)return R;const j=ze(t),K=Pc(j),p=(r=s.serializer)!=null?r:Ic[K],{pause:g,resume:A}=xc(R,()=>U(R.value),{flush:i,deep:l,eventFilter:w});He(J,()=>k(),{flush:i}),h&&o&&xi(()=>{n instanceof Storage?rs(h,"storage",k,{passive:!0}):rs(h,cr,L),O&&k()}),O||k();function B(M,D){if(h){const W={key:J.value,oldValue:M,newValue:D,storageArea:n};h.dispatchEvent(n instanceof Storage?new StorageEvent("storage",W):new CustomEvent(cr,{detail:W}))}}function U(M){try{const D=n.getItem(J.value);if(M==null)B(D,null),n.removeItem(J.value);else{const W=p.write(M);D!==W&&(n.setItem(J.value,W),B(D,W))}}catch(D){x(D)}}function q(M){const D=M?M.newValue:n.getItem(J.value);if(D==null)return c&&j!=null&&n.setItem(J.value,p.write(j)),j;if(!M&&d){const W=p.read(D);return typeof d=="function"?d(W,j):K==="object"&&!Array.isArray(W)?{...j,...W}:W}else return typeof D!="string"?D:p.read(D)}function k(M){if(!(M&&M.storageArea!==n)){if(M&&M.key==null){R.value=j;return}if(!(M&&M.key!==J.value)){g();try{(M==null?void 0:M.newValue)!==p.write(R.value)&&(R.value=q(M))}catch(D){x(D)}finally{M?xn(A):A()}}}}function L(M){k(M.detail)}return R}const Lc="*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}";function Nc(e={}){const{selector:t="html",attribute:n="class",initialValue:s="auto",window:r=Ht,storage:i,storageKey:l="vueuse-color-scheme",listenToStorageChanges:o=!0,storageRef:c,emitAuto:d,disableTransition:a=!0}=e,h={auto:"",light:"light",dark:"dark",...e.modes||{}},w=Ti({window:r}),x=se(()=>w.value?"dark":"light"),O=c||(l==null?wi(s):Fc(l,s,i,{window:r,listenToStorageChanges:o})),R=se(()=>O.value==="auto"?x.value:O.value),J=Ei("updateHTMLAttrs",(g,A,B)=>{const U=typeof g=="string"?r==null?void 0:r.document.querySelector(g):Si(g);if(!U)return;const q=new Set,k=new Set;let L=null;if(A==="class"){const D=B.split(/\s/g);Object.values(h).flatMap(W=>(W||"").split(/\s/g)).filter(Boolean).forEach(W=>{D.includes(W)?q.add(W):k.add(W)})}else L={key:A,value:B};if(q.size===0&&k.size===0&&L===null)return;let M;a&&(M=r.document.createElement("style"),M.appendChild(document.createTextNode(Lc)),r.document.head.appendChild(M));for(const D of q)U.classList.add(D);for(const D of k)U.classList.remove(D);L&&U.setAttribute(L.key,L.value),a&&(r.getComputedStyle(M).opacity,document.head.removeChild(M))});function j(g){var A;J(t,n,(A=h[g])!=null?A:g)}function K(g){e.onChanged?e.onChanged(g,j):j(g)}He(R,K,{flush:"post",immediate:!0}),xi(()=>K(R.value));const p=se({get(){return d?O.value:R.value},set(g){O.value=g}});return Object.assign(p,{store:O,system:x,state:R})}function jc(e={}){const{valueDark:t="dark",valueLight:n=""}=e,s=Nc({...e,onChanged:(l,o)=>{var c;e.onChanged?(c=e.onChanged)==null||c.call(e,l==="dark",o,l):o(l)},modes:{dark:t,light:n}}),r=se(()=>s.system.value);return se({get(){return s.value==="dark"},set(l){const o=l?"dark":"light";r.value===o?s.value="auto":s.value=o}})}const Wn={};var Bn={};const Ci=/^(?:[a-z]+:|\/\/)/i,Hc="vitepress-theme-appearance",$c=/#.*$/,Dc=/[?#].*$/,Uc=/(?:(^|\/)index)?\.(?:md|html)$/,ue=typeof document<"u",Ai={relativePath:"404.md",filePath:"",title:"404",description:"Not Found",headers:[],frontmatter:{sidebar:!1,layout:"page"},lastUpdated:0,isNotFound:!0};function Vc(e,t,n=!1){if(t===void 0)return!1;if(e=fr(`/${e}`),n)return new RegExp(t).test(e);if(fr(t)!==e)return!1;const s=t.match($c);return s?(ue?location.hash:"")===s[0]:!0}function fr(e){return decodeURI(e).replace(Dc,"").replace(Uc,"$1")}function Wc(e){return Ci.test(e)}function Bc(e,t){return Object.keys((e==null?void 0:e.locales)||{}).find(n=>n!=="root"&&!Wc(n)&&Vc(t,`/${n}/`,!0))||"root"}function kc(e,t){var s,r,i,l,o,c,d;const n=Bc(e,t);return Object.assign({},e,{localeIndex:n,lang:((s=e.locales[n])==null?void 0:s.lang)??e.lang,dir:((r=e.locales[n])==null?void 0:r.dir)??e.dir,title:((i=e.locales[n])==null?void 0:i.title)??e.title,titleTemplate:((l=e.locales[n])==null?void 0:l.titleTemplate)??e.titleTemplate,description:((o=e.locales[n])==null?void 0:o.description)??e.description,head:Oi(e.head,((c=e.locales[n])==null?void 0:c.head)??[]),themeConfig:{...e.themeConfig,...(d=e.locales[n])==null?void 0:d.themeConfig}})}function Ri(e,t){const n=t.title||e.title,s=t.titleTemplate??e.titleTemplate;if(typeof s=="string"&&s.includes(":title"))return s.replace(/:title/g,n);const r=Kc(e.title,s);return n===r.slice(3)?n:`${n}${r}`}function Kc(e,t){return t===!1?"":t===!0||t===void 0?` | ${e}`:e===t?"":` | ${t}`}function qc(e,t){const[n,s]=t;if(n!=="meta")return!1;const r=Object.entries(s)[0];return r==null?!1:e.some(([i,l])=>i===n&&l[r[0]]===r[1])}function Oi(e,t){return[...e.filter(n=>!qc(t,n)),...t]}const Gc=/[\u0000-\u001F"#$&*+,:;<=>?[\]^`{|}\u007F]/g,Jc=/^[a-z]:/i;function ar(e){const t=Jc.exec(e),n=t?t[0]:"";return n+e.slice(n.length).replace(Gc,"_").replace(/(^|\/)_+(?=[^/]*$)/,"$1")}const kn=new Set;function Xc(e){if(kn.size===0){const n=typeof process=="object"&&(Bn==null?void 0:Bn.VITE_EXTRA_EXTENSIONS)||(Wn==null?void 0:Wn.VITE_EXTRA_EXTENSIONS)||"";("3g2,3gp,aac,ai,apng,au,avif,bin,bmp,cer,class,conf,crl,css,csv,dll,doc,eps,epub,exe,gif,gz,ics,ief,jar,jpe,jpeg,jpg,js,json,jsonld,m4a,man,mid,midi,mjs,mov,mp2,mp3,mp4,mpe,mpeg,mpg,mpp,oga,ogg,ogv,ogx,opus,otf,p10,p7c,p7m,p7s,pdf,png,ps,qt,roff,rtf,rtx,ser,svg,t,tif,tiff,tr,ts,tsv,ttf,txt,vtt,wav,weba,webm,webp,woff,woff2,xhtml,xml,yaml,yml,zip"+(n&&typeof n=="string"?","+n:"")).split(",").forEach(s=>kn.add(s))}const t=e.split(".").pop();return t==null||!kn.has(t.toLowerCase())}const zc=Symbol(),ct=_t(uc);function bf(e){const t=se(()=>kc(ct.value,e.data.relativePath)),n=t.value.appearance,s=n==="force-dark"?rt(!0):n==="force-auto"?Ti():n?jc({storageKey:Hc,initialValue:()=>n==="dark"?"dark":"auto",...typeof n=="object"?n:{}}):rt(!1),r=rt(ue?location.hash:"");return ue&&window.addEventListener("hashchange",()=>{r.value=location.hash}),He(()=>e.data,()=>{r.value=ue?location.hash:""}),{site:t,theme:se(()=>t.value.themeConfig),page:se(()=>e.data),frontmatter:se(()=>e.data.frontmatter),params:se(()=>e.data.params),lang:se(()=>t.value.lang),dir:se(()=>e.data.frontmatter.dir||t.value.dir),localeIndex:se(()=>t.value.localeIndex||"root"),title:se(()=>Ri(t.value,e.data)),description:se(()=>e.data.description||t.value.description),isDark:s,hash:se(()=>r.value)}}function Yc(){const e=ot(zc);if(!e)throw new Error("vitepress data not properly injected in app");return e}function Qc(e,t){return`${e}${t}`.replace(/\/+/g,"/")}function ur(e){return Ci.test(e)||!e.startsWith("/")?e:Qc(ct.value.base,e)}function Zc(e){let t=e.replace(/\.html$/,"");if(t=decodeURIComponent(t),t=t.replace(/\/$/,"/index"),ue){const n="/blog/";t=ar(t.slice(n.length).replace(/\//g,"_")||"index")+".md";let s=__VP_HASH_MAP__[t.toLowerCase()];if(s||(t=t.endsWith("_index.md")?t.slice(0,-9)+".md":t.slice(0,-3)+"_index.md",s=__VP_HASH_MAP__[t.toLowerCase()]),!s)return null;t=`${n}assets/${t}.${s}.js`}else t=`./${ar(t.slice(1).replace(/\//g,"_"))}.md.js`;return t}let ef=[];function tf(){let e=ct.value.scrollOffset,t=0,n=24;if(typeof e=="object"&&"padding"in e&&(n=e.padding,e=e.selector),typeof e=="number")t=e;else if(typeof e=="string")t=dr(e,n);else if(Array.isArray(e))for(const s of e){const r=dr(s,n);if(r){t=r;break}}return t}function dr(e,t){const n=document.querySelector(e);if(!n)return 0;const s=n.getBoundingClientRect().bottom;return s<0?0:s+t}const nf=Symbol(),Mi="http://a.com",sf=()=>({path:"/",component:null,data:Ai});function yf(e,t){const n=yn(sf()),s={route:n,go:r};async function r(o=ue?location.href:"/"){var c,d;o=Kn(o),await((c=s.onBeforeRouteChange)==null?void 0:c.call(s,o))!==!1&&(ue&&o!==Kn(location.href)&&(history.replaceState({scrollPosition:window.scrollY},""),history.pushState({},"",o)),await l(o),await((d=s.onAfterRouteChange??s.onAfterRouteChanged)==null?void 0:d(o)))}let i=null;async function l(o,c=0,d=!1){var w,x;if(await((w=s.onBeforePageLoad)==null?void 0:w.call(s,o))===!1)return;const a=new URL(o,Mi),h=i=a.pathname;try{let O=await e(h);if(!O)throw new Error(`Page not found: ${h}`);if(i===h){i=null;const{default:R,__pageData:J}=O;if(!R)throw new Error(`Invalid route component: ${R}`);await((x=s.onAfterPageLoad)==null?void 0:x.call(s,o)),n.path=ue?h:ur(h),n.component=en(R),n.data=en(J),ue&&xn(()=>{let j=ct.value.base+J.relativePath.replace(/(?:(^|\/)index)?\.md$/,"$1");if(!ct.value.cleanUrls&&!j.endsWith("/")&&(j+=".html"),j!==a.pathname&&(a.pathname=j,o=j+a.search+a.hash,history.replaceState({},"",o)),a.hash&&!c){let K=null;try{K=document.getElementById(decodeURIComponent(a.hash).slice(1))}catch(p){console.warn(p)}if(K){hr(K,a.hash);return}}window.scrollTo(0,c)})}}catch(O){if(!/fetch|Page not found/.test(O.message)&&!/^\/404(\.html|\/)?$/.test(o)&&console.error(O),!d)try{const R=await fetch(ct.value.base+"hashmap.json");window.__VP_HASH_MAP__=await R.json(),await l(o,c,!0);return}catch{}if(i===h){i=null,n.path=ue?h:ur(h),n.component=t?en(t):null;const R=ue?h.replace(/(^|\/)$/,"$1index").replace(/(\.html)?$/,".md").replace(/^\//,""):"404.md";n.data={...Ai,relativePath:R}}}}return ue&&(history.state===null&&history.replaceState({},""),window.addEventListener("click",o=>{if(o.defaultPrevented||!(o.target instanceof Element)||o.target.closest("button")||o.button!==0||o.ctrlKey||o.shiftKey||o.altKey||o.metaKey)return;const c=o.target.closest("a");if(!c||c.closest(".vp-raw")||c.hasAttribute("download")||c.hasAttribute("target"))return;const d=c.getAttribute("href")??(c instanceof SVGAElement?c.getAttribute("xlink:href"):null);if(d==null)return;const{href:a,origin:h,pathname:w,hash:x,search:O}=new URL(d,c.baseURI),R=new URL(location.href);h===R.origin&&Xc(w)&&(o.preventDefault(),w===R.pathname&&O===R.search?(x!==R.hash&&(history.pushState({},"",a),window.dispatchEvent(new HashChangeEvent("hashchange",{oldURL:R.href,newURL:a}))),x?hr(c,x,c.classList.contains("header-anchor")):window.scrollTo(0,0)):r(a))},{capture:!0}),window.addEventListener("popstate",async o=>{var d;if(o.state===null)return;const c=Kn(location.href);await l(c,o.state&&o.state.scrollPosition||0),await((d=s.onAfterRouteChange??s.onAfterRouteChanged)==null?void 0:d(c))}),window.addEventListener("hashchange",o=>{o.preventDefault()})),s}function rf(){const e=ot(nf);if(!e)throw new Error("useRouter() is called without provider.");return e}function Pi(){return rf().route}function hr(e,t,n=!1){let s=null;try{s=e.classList.contains("header-anchor")?e:document.getElementById(decodeURIComponent(t).slice(1))}catch(r){console.warn(r)}if(s){let r=function(){!n||Math.abs(l-window.scrollY)>window.innerHeight?window.scrollTo(0,l):window.scrollTo({left:0,top:l,behavior:"smooth"})};const i=parseInt(window.getComputedStyle(s).paddingTop,10),l=window.scrollY+s.getBoundingClientRect().top-tf()+i;requestAnimationFrame(r)}}function Kn(e){const t=new URL(e,Mi);return t.pathname=t.pathname.replace(/(^|\/)index(\.html)?$/,"$1"),ct.value.cleanUrls?t.pathname=t.pathname.replace(/\.html$/,""):!t.pathname.endsWith("/")&&!t.pathname.endsWith(".html")&&(t.pathname+=".html"),t.pathname+t.search+t.hash}const Qt=()=>ef.forEach(e=>e()),vf=Wr({name:"VitePressContent",props:{as:{type:[Object,String],default:"div"}},setup(e){const t=Pi(),{frontmatter:n,site:s}=Yc();return He(n,Qt,{deep:!0,flush:"post"}),()=>Js(e.as,s.value.contentProps??{style:{position:"relative"}},[t.component?Js(t.component,{onVnodeMounted:Qt,onVnodeUpdated:Qt,onVnodeUnmounted:Qt}):"404 Page Not Found"])}}),wf=(e,t)=>{const n=e.__vccOpts||e;for(const[s,r]of t)n[s]=r;return n},xf=Wr({setup(e,{slots:t}){const n=rt(!1);return Ut(()=>{n.value=!0}),()=>n.value&&t.default?t.default():null}});function Sf(){ue&&window.addEventListener("click",e=>{var n;const t=e.target;if(t.matches(".vp-code-group input")){const s=(n=t.parentElement)==null?void 0:n.parentElement;if(!s)return;const r=Array.from(s.querySelectorAll("input")).indexOf(t);if(r<0)return;const i=s.querySelector(".blocks");if(!i)return;const l=Array.from(i.children).find(d=>d.classList.contains("active"));if(!l)return;const o=i.children[r];if(!o||l===o)return;l.classList.remove("active"),o.classList.add("active");const c=s==null?void 0:s.querySelector(`label[for="${t.id}"]`);c==null||c.scrollIntoView({block:"nearest"})}})}function Ef(){if(ue){const e=new WeakMap;window.addEventListener("click",t=>{var s;const n=t.target;if(n.matches('div[class*="language-"] > button.copy')){const r=n.parentElement,i=(s=n.nextElementSibling)==null?void 0:s.nextElementSibling;if(!r||!i)return;const l=/language-(shellscript|shell|bash|sh|zsh)/.test(r.className),o=[".vp-copy-ignore",".diff.remove"],c=i.cloneNode(!0);c.querySelectorAll(o.join(",")).forEach(a=>a.remove());let d=c.textContent||"";l&&(d=d.replace(/^ *(\$|>) /gm,"").trim()),of(d).then(()=>{n.classList.add("copied"),clearTimeout(e.get(n));const a=setTimeout(()=>{n.classList.remove("copied"),n.blur(),e.delete(n)},2e3);e.set(n,a)})}})}}async function of(e){try{return navigator.clipboard.writeText(e)}catch{const t=document.createElement("textarea"),n=document.activeElement;t.value=e,t.setAttribute("readonly",""),t.style.contain="strict",t.style.position="absolute",t.style.left="-9999px",t.style.fontSize="12pt";const s=document.getSelection(),r=s?s.rangeCount>0&&s.getRangeAt(0):null;document.body.appendChild(t),t.select(),t.selectionStart=0,t.selectionEnd=e.length,document.execCommand("copy"),document.body.removeChild(t),r&&(s.removeAllRanges(),s.addRange(r)),n&&n.focus()}}function Tf(e,t){let n=!0,s=[];const r=i=>{if(n){n=!1,i.forEach(o=>{const c=qn(o);for(const d of document.head.children)if(d.isEqualNode(c)){s.push(d);return}});return}const l=i.map(qn);s.forEach((o,c)=>{const d=l.findIndex(a=>a==null?void 0:a.isEqualNode(o??null));d!==-1?delete l[d]:(o==null||o.remove(),delete s[c])}),l.forEach(o=>o&&document.head.appendChild(o)),s=[...s,...l].filter(Boolean)};li(()=>{const i=e.data,l=t.value,o=i&&i.description,c=i&&i.frontmatter.head||[],d=Ri(l,i);d!==document.title&&(document.title=d);const a=o||l.description;let h=document.querySelector("meta[name=description]");h?h.getAttribute("content")!==a&&h.setAttribute("content",a):qn(["meta",{name:"description",content:a}]),r(Oi(l.head,cf(c)))})}function qn([e,t,n]){const s=document.createElement(e);for(const r in t)s.setAttribute(r,t[r]);return n&&(s.innerHTML=n),e==="script"&&t.async==null&&(s.async=!1),s}function lf(e){return e[0]==="meta"&&e[1]&&e[1].name==="description"}function cf(e){return e.filter(t=>!lf(t))}const Gn=new Set,Ii=()=>document.createElement("link"),ff=e=>{const t=Ii();t.rel="prefetch",t.href=e,document.head.appendChild(t)},af=e=>{const t=new XMLHttpRequest;t.open("GET",e,t.withCredentials=!0),t.send()};let Zt;const uf=ue&&(Zt=Ii())&&Zt.relList&&Zt.relList.supports&&Zt.relList.supports("prefetch")?ff:af;function Cf(){if(!ue||!window.IntersectionObserver)return;let e;if((e=navigator.connection)&&(e.saveData||/2g/.test(e.effectiveType)))return;const t=window.requestIdleCallback||setTimeout;let n=null;const s=()=>{n&&n.disconnect(),n=new IntersectionObserver(i=>{i.forEach(l=>{if(l.isIntersecting){const o=l.target;n.unobserve(o);const{pathname:c}=o;if(!Gn.has(c)){Gn.add(c);const d=Zc(c);d&&uf(d)}}})}),t(()=>{document.querySelectorAll("#app a").forEach(i=>{const{hostname:l,pathname:o}=new URL(i.href instanceof SVGAnimatedString?i.href.animVal:i.href,i.baseURI),c=o.match(/\.\w+$/);c&&c[0]!==".html"||i.target!=="_blank"&&l===location.hostname&&(o!==location.pathname?n.observe(i):Gn.add(o))})})};Ut(s);const r=Pi();He(()=>r.path,s),vs(()=>{n&&n.disconnect()})}export{Cf as A,Ef as B,vf as C,Sf as D,Js as E,Me as F,nf as R,wf as _,gi as a,_s as b,pf as c,Wr as d,he as e,mf as f,Pi as g,se as h,df as i,Al as j,gf as k,ue as l,Tf as m,bf as n,Tl as o,zc as p,xf as q,hf as r,ct as s,Gi as t,Yc as u,yf as v,Zc as w,_f as x,Ut as y,li as z};
