<?xml version="1.0" encoding="utf-8"?>
<rss version="2.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:content="http://purl.org/rss/1.0/modules/content/">
    <channel>
        <title>The Vue Point</title>
        <link>https://gomark.pro/blog</link>
        <description>The official blog for the Vue.js project</description>
        <lastBuildDate>Thu, 07 Aug 2025 10:28:05 GMT</lastBuildDate>
        <docs>https://validator.w3.org/feed/docs/rss2.html</docs>
        <generator>https://github.com/jpmonette/feed</generator>
        <language>en</language>
        <image>
            <title>The Vue Point</title>
            <url>https://vuejs.org/images/logo.png</url>
            <link>https://gomark.pro/blog</link>
        </image>
        <copyright>Copyright (c) 2021-present, <PERSON><PERSON> (<PERSON>) You and blog contributors</copyright>
        <item>
            <title><![CDATA[如何在博客中使用图片]]></title>
            <link>https://gomark.pro/blog/posts/how-to-use-images.html</link>
            <guid isPermaLink="false">https://gomark.pro/blog/posts/how-to-use-images.html</guid>
            <pubDate>Thu, 07 Aug 2025 00:00:00 GMT</pubDate>
            <content:encoded><![CDATA[<h1 id="如何在博客中使用图片" tabindex="-1">如何在博客中使用图片 <a class="header-anchor" href="#如何在博客中使用图片" aria-label="Permalink to &quot;如何在博客中使用图片&quot;"></a></h1>
<p>这篇文章演示了如何在 VitePress 博客中使用图片资源。</p>
<h2 id="使用-public-目录中的图片" tabindex="-1">使用 public 目录中的图片 <a class="header-anchor" href="#使用-public-目录中的图片" aria-label="Permalink to &quot;使用 public 目录中的图片&quot;"></a></h2>
<p>VitePress 会自动处理 <code>blog/public</code> 目录下的静态资源。你可以直接引用这些文件：</p>
<h3 id="示例-引用-logo" tabindex="-1">示例：引用 logo <a class="header-anchor" href="#示例-引用-logo" aria-label="Permalink to &quot;示例：引用 logo&quot;"></a></h3>
<div class="language-markdown vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">markdown</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0" v-pre=""><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8">![</span><span style="--shiki-light:#032F62;--shiki-light-text-decoration:underline;--shiki-dark:#DBEDFF;--shiki-dark-text-decoration:underline">Logo</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8">](</span><span style="--shiki-light:#24292E;--shiki-light-text-decoration:underline;--shiki-dark:#E1E4E8;--shiki-dark-text-decoration:underline">../public/images/pic.svg</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8">)</span></span></code></pre>
</div><p>实际效果：</p>
<p><img src="./../public/images/pic.svg" alt="Logo"></p>
<h2 id="图片路径规则" tabindex="-1">图片路径规则 <a class="header-anchor" href="#图片路径规则" aria-label="Permalink to &quot;图片路径规则&quot;"></a></h2>
<ol>
<li>
<p><strong>绝对路径</strong>：以 <code>/blog/</code> 开头，指向 <code>blog/public</code> 目录</p>
<div class="language-markdown vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">markdown</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0" v-pre=""><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8">![</span><span style="--shiki-light:#032F62;--shiki-light-text-decoration:underline;--shiki-dark:#DBEDFF;--shiki-dark-text-decoration:underline">图片描述</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8">](</span><span style="--shiki-light:#24292E;--shiki-light-text-decoration:underline;--shiki-dark:#E1E4E8;--shiki-dark-text-decoration:underline">/your-image.png</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8">)</span></span></code></pre>
</div></li>
<li>
<p><strong>相对路径</strong>：相对于当前 markdown 文件</p>
<div class="language-markdown vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">markdown</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0" v-pre=""><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8">![</span><span style="--shiki-light:#032F62;--shiki-light-text-decoration:underline;--shiki-dark:#DBEDFF;--shiki-dark-text-decoration:underline">图片描述</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8">](</span><span style="--shiki-light:#24292E;--shiki-light-text-decoration:underline;--shiki-dark:#E1E4E8;--shiki-dark-text-decoration:underline">./images/your-image.png</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8">)</span></span></code></pre>
</div></li>
</ol>
<h2 id="添加新图片的步骤" tabindex="-1">添加新图片的步骤 <a class="header-anchor" href="#添加新图片的步骤" aria-label="Permalink to &quot;添加新图片的步骤&quot;"></a></h2>
<ol>
<li>将图片文件放入 <code>blog/public</code> 目录</li>
<li>在 markdown 文件中使用 <code>/blog/filename.ext</code> 路径引用</li>
<li>运行 <code>pnpm blog:build</code> 重新构建</li>
</ol>
<h2 id="图片优化建议" tabindex="-1">图片优化建议 <a class="header-anchor" href="#图片优化建议" aria-label="Permalink to &quot;图片优化建议&quot;"></a></h2>
<ul>
<li>使用适当的图片格式（PNG、JPG、SVG、WebP）</li>
<li>压缩图片以减少文件大小</li>
<li>为图片添加有意义的 alt 文本</li>
<li>考虑使用响应式图片</li>
</ul>
<h2 id="html-标签方式" tabindex="-1">HTML 标签方式 <a class="header-anchor" href="#html-标签方式" aria-label="Permalink to &quot;HTML 标签方式&quot;"></a></h2>
<p>你也可以使用 HTML <code>&lt;img&gt;</code> 标签获得更多控制：</p>
<div class="language-html vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">html</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0" v-pre=""><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8">&#x3C;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D">img</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0"> src</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF">"./images/pic.svg"</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0"> alt</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF">"Logo"</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0"> width</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF">"100"</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0"> height</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF">"100"</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8"> /></span></span></code></pre>
</div><p>这样你就可以在博客文章中自由使用图片了！</p>
]]></content:encoded>
        </item>
        <item>
            <title><![CDATA[2022 Year In Review]]></title>
            <link>https://gomark.pro/blog/posts/2022-year-in-review.html</link>
            <guid isPermaLink="false">https://gomark.pro/blog/posts/2022-year-in-review.html</guid>
            <pubDate>Sun, 01 Jan 2023 00:00:00 GMT</pubDate>
            <description><![CDATA[<p>Happy new year, Vue community! With 2023 upon us, we would like to take this opportunity to recap what happened in 2022, and discuss what to expect in 2023.</p>
]]></description>
            <content:encoded><![CDATA[<p>Happy new year, Vue community! With 2023 upon us, we would like to take this opportunity to recap what happened in 2022, and discuss what to expect in 2023.</p>
<hr>
<h2 id="recap-for-2022" tabindex="-1">Recap for 2022 <a class="header-anchor" href="#recap-for-2022" aria-label="Permalink to &quot;Recap for 2022&quot;"></a></h2>
<p>In February 2022, we <a href="./hello-2021.html">switched Vue's default version to 3.x</a>. The switch marked the readiness of all the official parts of the framework for v3, including a major revamp of the documentation that provides guidance on latest best practices.</p>
<p>We are still in a transition period for the ecosystem to move to Vue 3. So after the switch, we focused more on improving Vue's developer experience by investing in tooling. Our team members have been actively involved in the development of <a href="https://vitejs.dev" target="_blank" rel="noreferrer">Vite</a>, and we made significant improvement to Vue's IDE and TypeScript support by shipping <a href="./hello-2021.html">Volar 1.0</a>.</p>
<p>Over the course of 2022, we saw the NPM usage of Vue 3 grew by <strong>almost 200%</strong>. On the community side, the Vue 3 ecosystem is now ripe with great solutions to help boost your productivity. Both <a href="https://nuxt.com" target="_blank" rel="noreferrer">Nuxt 3</a> and <a href="https://vuetifyjs.com" target="_blank" rel="noreferrer">Vuetify 3</a> reached stable status in November 2022, and <a href="https://github.com/nativescript-vue/nativescript-vue" target="_blank" rel="noreferrer">NativeScript for Vue 3</a> recently launched beta. In addition, we want to give a shout out to other great projects that had already supported Vue 3 for quite some time: <a href="https://quasar.dev/" target="_blank" rel="noreferrer">Quasar</a>, <a href="https://www.naiveui.com/" target="_blank" rel="noreferrer">NaiveUI</a>, <a href="https://ionicframework.com/docs/vue/overview" target="_blank" rel="noreferrer">Ionic Vue</a>, <a href="https://www.primefaces.org/primevue/" target="_blank" rel="noreferrer">PrimeVue</a>, <a href="https://www.inkline.io/" target="_blank" rel="noreferrer">InkLine</a>, <a href="https://element-plus.org/" target="_blank" rel="noreferrer">ElementPlus</a>, and <a href="https://twitter.com/vuejs/status/1599706412086878208" target="_blank" rel="noreferrer">more</a>.</p>
<p>Despite Vue 3 being now the default, we understand that many users have to stay on Vue 2 due to the cost of migration. To ensure that Vue 2 users benefit from the advancement of the framework, we decided to move Vue 2's source code to TypeScript and back-ported some of the most important Vue 3 features in <a href="./hello-2021.html">Vue 2.7</a>. We also made sure that Vite, Vue Devtools and Volar all simultaneously support Vue 2 and Vue 3.</p>
<h2 id="what-to-expect-in-2023" tabindex="-1">What to Expect in 2023 <a class="header-anchor" href="#what-to-expect-in-2023" aria-label="Permalink to &quot;What to Expect in 2023&quot;"></a></h2>
<h3 id="smaller-and-more-frequent-minor-releases" tabindex="-1">Smaller and More Frequent Minor Releases <a class="header-anchor" href="#smaller-and-more-frequent-minor-releases" aria-label="Permalink to &quot;Smaller and More Frequent Minor Releases&quot;"></a></h3>
<p>With the last Vue 2 minor release (2.7) out of the door, we expect to be full steam ahead shipping features for Vue 3 core in 2023. We have quite a long list of features that we are excited to work on!</p>
<p>One thing we would like to improve is our release cadence. Vue follows <a href="https://semver.org/" target="_blank" rel="noreferrer">semver</a>, which means we should only ship features in minor versions. In the past, we did a &quot;big minor&quot; approach where we group many features together in big, infrequent minor releases. This has resulted in quite some low-complexity features being blocked while we worked on other high-complexity ones. In 2023, we want to do smaller and more frequent minor releases so that we can get more features out, faster.</p>
<p>This also means we will be adjusting what goes into 3.3. Originally, we planned to graduate Suspense and Reactivity Transform from experimental status in 3.3. However, we feel that both still need further RFC discussion, and they should not block other more straightforward features to land. Now, the goal of 3.3 is to land proposed / planned features that are clear wins and do not require RFC discussion - for example, supporting externally imported types in <code>&lt;script setup&gt;</code> macros.</p>
<p>In parallel to that, we will:</p>
<ol>
<li>Further evaluate the readiness of Suspense and Reactivity Transform.</li>
<li>Spend time to evaluate outstanding user-submitted RFCs and feature requests.</li>
<li>Post RFCs for features that we intend to land in 3.4 and beyond, for example SSR lazy hydration.</li>
</ol>
<p>Expect more details later this month.</p>
<p>Another thing to note is there is no plan for big breaking changes for the foreseeable future. Acknowledging the challenges users faced during the v2 to v3 transition, we want to have a better long term upgrade story for Vue going forward.</p>
<h3 id="vapor-mode" tabindex="-1">Vapor Mode <a class="header-anchor" href="#vapor-mode" aria-label="Permalink to &quot;Vapor Mode&quot;"></a></h3>
<p>Vapor Mode is an alternative compilation strategy that we have been experimenting with, inspired by <a href="https://www.solidjs.com/" target="_blank" rel="noreferrer">Solid</a>. Given the same Vue SFC, Vapor Mode compiles it into JavaScript output that is more performant, uses less memory, and requires less runtime support code compared to the current Virtual DOM based output. It is still in early phase, but here are some high level points:</p>
<ul>
<li>
<p>Vapor Mode is intended for use cases where performance is the primary concern. It is opt-in and does not affect existing codebases.</p>
</li>
<li>
<p>At the very least, you will be able to embed a Vapor component subtree into any existing Vue 3 app. Ideally, we hope to achieve granular opt-in at the component level, which means freely mixing Vapor and non-Vapor components in the same app.</p>
</li>
<li>
<p>Building an app with only Vapor components allows you to drop the Virtual DOM runtime from the bundle, significantly reducing the baseline runtime size.</p>
</li>
<li>
<p>In order to achieve the best performance, Vapor Mode will only support a subset of Vue features. In particular, Vapor Mode components will only support Composition API and <code>&lt;script setup&gt;</code>. However, this supported subset will work exactly the same between Vapor and non-Vapor components.</p>
</li>
</ul>
<p>We will share more details as we make more progress later in the year.</p>
<h3 id="conferences" tabindex="-1">Conferences <a class="header-anchor" href="#conferences" aria-label="Permalink to &quot;Conferences&quot;"></a></h3>
<p>There are already many in-person Vue conferences lined up for 2023:</p>
<ul>
<li><a href="https://vuejs.amsterdam/" target="_blank" rel="noreferrer">Vue.js Amsterdam</a> - Feb 9-10, Amsterdam, The Netherlands</li>
<li><a href="https://vuejslive.com/" target="_blank" rel="noreferrer">Vue.js Live</a> - May 12 &amp; 15th, London, UK</li>
<li><a href="https://us.vuejs.org/" target="_blank" rel="noreferrer">VueConf US</a> - May 24-26th, New Orleans, USA</li>
<li>VueFes Japan - October 28th, Tokyo, Japan (info TBA)</li>
</ul>
<p>I (Evan) plan to attend all of these in person. After almost 3 years of absence, I can't wait to meet the community again - please come say hi!</p>
<h3 id="one-year-until-vue-2-eol" tabindex="-1">One Year Until Vue 2 EOL <a class="header-anchor" href="#one-year-until-vue-2-eol" aria-label="Permalink to &quot;One Year Until Vue 2 EOL&quot;"></a></h3>
<p>As a reminder, today marks <strong>exactly one year until the end of Vue 2 support</strong>. We have created a page explaining the implication of this and outlining the options for those who expect to be using Vue 2 beyond the EOL date: <a href="https://v2.vuejs.org/lts/" target="_blank" rel="noreferrer">Details on Vue 2 EOL and Extended Support</a>.</p>
]]></content:encoded>
        </item>
        <item>
            <title><![CDATA[Reflections for 2020-2021]]></title>
            <link>https://gomark.pro/blog/posts/hello-2021.html</link>
            <guid isPermaLink="false">https://gomark.pro/blog/posts/hello-2021.html</guid>
            <pubDate>Mon, 11 Jan 2021 00:00:00 GMT</pubDate>
            <description><![CDATA[<p>With a new year upon us, we also have a new blog! In this post, we would like to take a look back at some of the highlights from 2020, as well as some ideas we have for 2021.</p>
]]></description>
            <content:encoded><![CDATA[<p>With a new year upon us, we also have a new blog! In this post, we would like to take a look back at some of the highlights from 2020, as well as some ideas we have for 2021.</p>
<hr>
<h2 id="looking-back-at-2020" tabindex="-1">Looking Back at 2020 <a class="header-anchor" href="#looking-back-at-2020" aria-label="Permalink to &quot;Looking Back at 2020&quot;"></a></h2>
<p>2020 has been a challenging year, to say the least. Nonetheless, the team has made the best of a difficult situation. Despite an already massive user base, Vue's <a href="https://npm-stat.com/charts.html?package=vue&amp;from=2020-01-01&amp;to=2020-12-31" target="_blank" rel="noreferrer">NPM downloads</a> and <a href="https://chrome-stats.com/d/nhdogjmejiglipccpnnnanhbledajbpd" target="_blank" rel="noreferrer">Devtools weekly active users</a> both grew close to 50% throughout 2020. As time of this writing, Vue is being downloaded ~8M times per month on NPM and the devtools extension has ~1.5M weekly active users.</p>
<p>Aside from routine maintenance, there are some incredible things that we accomplished as a team:</p>
<ul>
<li><a href="https://github.com/vuejs/vue-next/releases/tag/v3.0.0" target="_blank" rel="noreferrer">Shipped Vue 3 core</a></li>
<li><a href="https://v3.vuejs.org/" target="_blank" rel="noreferrer">Brand new docs site for Vue 3</a></li>
<li><a href="https://github.com/vuejs/vue-router-next/releases/tag/v4.0.0" target="_blank" rel="noreferrer">Vue Router 4</a></li>
<li><a href="https://next.vuex.vuejs.org/" target="_blank" rel="noreferrer">Vuex 4 (RC)</a></li>
<li><a href="https://chrome.google.com/webstore/detail/vuejs-devtools/ljjemllljcmogpfapbkkighbhhppjdbg" target="_blank" rel="noreferrer">Vue DevTools 6.0 with Vue 3 support (Beta)</a></li>
<li><a href="https://vue-test-utils.vuejs.org/v2/guide/introduction.html" target="_blank" rel="noreferrer">Vue Test Utils 2 (Beta)</a></li>
</ul>
<p>In addition to iterating on the existing ecosystem, we also invested in exploring improvements on new frontiers:</p>
<ul>
<li>New Single File Component (SFC) feature proposals with the goal of leveraging the SFC compiler for more DX and performance wins:
<ul>
<li><a href="https://github.com/vuejs/rfcs/pull/227" target="_blank" rel="noreferrer"><code>&lt;script setup&gt;</code></a></li>
<li><a href="https://github.com/vuejs/rfcs/pull/231" target="_blank" rel="noreferrer">CSS variables injection in <code>&lt;style&gt;</code></a></li>
</ul>
</li>
<li><a href="https://github.com/znck/vue-developer-experience" target="_blank" rel="noreferrer">VueDX</a> for providing better IDE integrations and development workflow</li>
<li><a href="http://vitejs.dev/" target="_blank" rel="noreferrer">Vite</a>, a new build tool built on top of modern standards</li>
<li><a href="https://vitepress.vuejs.org/" target="_blank" rel="noreferrer">VitePress</a>, a new static site generator built on Vue 3 and Vite</li>
</ul>
<p>In addition to all of these exciting projects, it’s also been incredible to see the community continue to grow despite the challenges 2020 set forth in terms of being unable to facilitate in-person events. With initiatives such as remote conferences, meetups and other events, it’s been a joy to see the community interacting in new ways that might not have been possible otherwise.</p>
<h2 id="looking-forward-to-2021" tabindex="-1">Looking Forward to 2021 <a class="header-anchor" href="#looking-forward-to-2021" aria-label="Permalink to &quot;Looking Forward to 2021&quot;"></a></h2>
<p>While Vue 3 brings many fundamental improvements and opens up avenues for future iterations, we are still in a transition period - both in terms of the library ecosystem and best practices involving new APIs. For early 2021, our focus will be further stabilizing the Vue 3 ecosystem, and continuing to help the community through this transition period. Here is a non-exhaustive list of things we’re looking forward to:</p>
<ul>
<li>Stablize current RC/Beta sub projects</li>
<li>Finalize SFC proposals and further polish IDE support</li>
<li>Vue 3.1 (Q1, more details as we finalize the release plan)</li>
<li>Vue 2 → 3 Migration Tools (estimated end of Q1)</li>
<li>Vue CLI 5 w/ webpack 5 support (estimated Q1)</li>
<li><s>Vue 3 IE 11 Compatibility Build (estimated Q2)</s></li>
<li>Vue 2.7 (estimated Q2/3)</li>
<li>SSR support in Vite</li>
<li>Vuex 5 (TBD)</li>
</ul>
<p>In addition, we are excited about other frameworks and libraries in the ecosystem making progress towards Vue 3 support. And of course, we can’t wait to see what new ideas and tools you all have as we embark on a new chapter in the Vue.js roadmap.</p>
]]></content:encoded>
        </item>
    </channel>
</rss>