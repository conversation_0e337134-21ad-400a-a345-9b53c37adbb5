<!DOCTYPE html>
<html lang="en-US" dir="ltr">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title>Reflections for 2020-2021 | The Vue Point</title>
    <meta name="description" content="The official blog for the Vue.js project">
    <meta name="generator" content="VitePress v1.6.4">
    <link rel="preload stylesheet" href="/blog/assets/style.BsMwNMTh.css" as="style">
    <link rel="preload stylesheet" href="/blog/vp-icons.css" as="style">
    
    <script type="module" src="/blog/assets/app.BLSFSP5b.js"></script>
    <link rel="modulepreload" href="/blog/assets/chunks/theme.C-9tmA-F.js">
    <link rel="modulepreload" href="/blog/assets/chunks/framework._wHCgU2P.js">
    <link rel="modulepreload" href="/blog/assets/posts_hello-2021.md.CWsHxIdM.lean.js">
    <meta name="twitter:site" content="@vuejs">
    <meta name="twitter:card" content="summary">
    <meta name="twitter:image" content="https://vuejs.org/images/logo.png">
    <script id="check-dark-mode">(()=>{const e=localStorage.getItem("vitepress-theme-appearance")||"auto",a=window.matchMedia("(prefers-color-scheme: dark)").matches;(!e||e==="auto"?a:e==="dark")&&document.documentElement.classList.add("dark")})();</script>
    <script id="check-mac-os">document.documentElement.classList.toggle("mac",/Mac|iPhone|iPod|iPad/i.test(navigator.platform));</script>
  </head>
  <body>
    <div id="app"><div class="antialiased dark:bg-slate-900"><div class="max-w-3xl mx-auto px-4 sm:px-6 xl:max-w-5xl xl:px-0"><nav class="flex justify-between items-center py-10 font-bold"><a class="text-xl" href="/blog/" aria-label="The Vue Point"><img class="inline-block mr-2" style="width:36px;height:31px;" alt="logo" src="/logo.svg"><span class="hidden md:inline dark:text-white">The Vue Point</span></a><div class="text-sm text-gray-500 dark:text-white leading-5"><a class="hover:text-gray-700 dark:hover:text-gray-200" href="https://github.com/vuejs/blog" target="_blank" rel="noopener"><span class="hidden sm:inline">GitHub </span>Source</a><span class="mr-2 ml-2">·</span><a class="hover:text-gray-700 dark:hover:text-gray-200 vp-raw" href="/feed.rss">RSS<span class="hidden sm:inline"> Feed</span></a><span class="mr-2 ml-2">·</span><a class="hover:text-gray-700 dark:hover:text-gray-200" href="https://vuejs.org" target="_blank" rel="noopener">Vuejs.org →</a></div></nav></div><main class="max-w-3xl mx-auto px-4 sm:px-6 xl:max-w-5xl xl:px-0"><article class="xl:divide-y xl:divide-gray-200 dark:xl:divide-slate-200/5"><header class="pt-6 xl:pb-10 space-y-1 text-center"><dl><dt class="sr-only">Published on</dt><dd class="text-base leading-6 font-medium text-gray-500 dark:text-gray-300"><time datetime="2021-01-11T12:00:00.000Z">January 11, 2021</time></dd></dl><h1 class="text-3xl leading-9 font-extrabold text-gray-900 dark:text-white tracking-tight sm:text-4xl sm:leading-10 md:text-5xl md:leading-14">Reflections for 2020-2021</h1></header><div class="divide-y xl:divide-y-0 divide-gray-200 dark:divide-slate-200/5 xl:grid xl:grid-cols-4 xl:gap-x-10 pb-16 xl:pb-20" style="grid-template-rows:auto 1fr;"><dl class="pt-6 pb-10 xl:pt-11 xl:border-b xl:border-gray-200 dark:xl:border-slate-200/5"><dt class="sr-only">Authors</dt><dd><ul class="flex justify-center xl:block space-x-8 sm:space-x-12 xl:space-x-0 xl:space-y-8"><li class="flex items-center space-x-2"><img src="https://gravatar.com/avatar/eca93da2c67aadafe35d477aa8f454b8" alt="author image" class="w-10 h-10 rounded-full"><dl class="text-sm font-medium leading-5 whitespace-nowrap"><dt class="sr-only">Name</dt><dd class="text-gray-900 dark:text-white">Evan You</dd><dt class="sr-only">Twitter</dt><dd><a href="https://twitter.com/@youyuxi" target="_blank" rel="noopnener noreferrer" class="link">@youyuxi</a></dd></dl></li></ul></dd></dl><div class="divide-y divide-gray-200 dark:divide-slate-200/5 xl:pb-0 xl:col-span-3 xl:row-span-2"><div style="position:relative;" class="prose dark:prose-invert max-w-none pt-10 pb-8"><div><p>With a new year upon us, we also have a new blog! In this post, we would like to take a look back at some of the highlights from 2020, as well as some ideas we have for 2021.</p><hr><h2 id="looking-back-at-2020" tabindex="-1">Looking Back at 2020 <a class="header-anchor" href="#looking-back-at-2020" aria-label="Permalink to &quot;Looking Back at 2020&quot;">​</a></h2><p>2020 has been a challenging year, to say the least. Nonetheless, the team has made the best of a difficult situation. Despite an already massive user base, Vue&#39;s <a href="https://npm-stat.com/charts.html?package=vue&amp;from=2020-01-01&amp;to=2020-12-31" target="_blank" rel="noreferrer">NPM downloads</a> and <a href="https://chrome-stats.com/d/nhdogjmejiglipccpnnnanhbledajbpd" target="_blank" rel="noreferrer">Devtools weekly active users</a> both grew close to 50% throughout 2020. As time of this writing, Vue is being downloaded ~8M times per month on NPM and the devtools extension has ~1.5M weekly active users.</p><p>Aside from routine maintenance, there are some incredible things that we accomplished as a team:</p><ul><li><a href="https://github.com/vuejs/vue-next/releases/tag/v3.0.0" target="_blank" rel="noreferrer">Shipped Vue 3 core</a></li><li><a href="https://v3.vuejs.org/" target="_blank" rel="noreferrer">Brand new docs site for Vue 3</a></li><li><a href="https://github.com/vuejs/vue-router-next/releases/tag/v4.0.0" target="_blank" rel="noreferrer">Vue Router 4</a></li><li><a href="https://next.vuex.vuejs.org/" target="_blank" rel="noreferrer">Vuex 4 (RC)</a></li><li><a href="https://chrome.google.com/webstore/detail/vuejs-devtools/ljjemllljcmogpfapbkkighbhhppjdbg" target="_blank" rel="noreferrer">Vue DevTools 6.0 with Vue 3 support (Beta)</a></li><li><a href="https://vue-test-utils.vuejs.org/v2/guide/introduction.html" target="_blank" rel="noreferrer">Vue Test Utils 2 (Beta)</a></li></ul><p>In addition to iterating on the existing ecosystem, we also invested in exploring improvements on new frontiers:</p><ul><li>New Single File Component (SFC) feature proposals with the goal of leveraging the SFC compiler for more DX and performance wins: <ul><li><a href="https://github.com/vuejs/rfcs/pull/227" target="_blank" rel="noreferrer"><code>&lt;script setup&gt;</code></a></li><li><a href="https://github.com/vuejs/rfcs/pull/231" target="_blank" rel="noreferrer">CSS variables injection in <code>&lt;style&gt;</code></a></li></ul></li><li><a href="https://github.com/znck/vue-developer-experience" target="_blank" rel="noreferrer">VueDX</a> for providing better IDE integrations and development workflow</li><li><a href="http://vitejs.dev/" target="_blank" rel="noreferrer">Vite</a>, a new build tool built on top of modern standards</li><li><a href="https://vitepress.vuejs.org/" target="_blank" rel="noreferrer">VitePress</a>, a new static site generator built on Vue 3 and Vite</li></ul><p>In addition to all of these exciting projects, it’s also been incredible to see the community continue to grow despite the challenges 2020 set forth in terms of being unable to facilitate in-person events. With initiatives such as remote conferences, meetups and other events, it’s been a joy to see the community interacting in new ways that might not have been possible otherwise.</p><h2 id="looking-forward-to-2021" tabindex="-1">Looking Forward to 2021 <a class="header-anchor" href="#looking-forward-to-2021" aria-label="Permalink to &quot;Looking Forward to 2021&quot;">​</a></h2><p>While Vue 3 brings many fundamental improvements and opens up avenues for future iterations, we are still in a transition period - both in terms of the library ecosystem and best practices involving new APIs. For early 2021, our focus will be further stabilizing the Vue 3 ecosystem, and continuing to help the community through this transition period. Here is a non-exhaustive list of things we’re looking forward to:</p><ul><li>Stablize current RC/Beta sub projects</li><li>Finalize SFC proposals and further polish IDE support</li><li>Vue 3.1 (Q1, more details as we finalize the release plan)</li><li>Vue 2 → 3 Migration Tools (estimated end of Q1)</li><li>Vue CLI 5 w/ webpack 5 support (estimated Q1)</li><li><s>Vue 3 IE 11 Compatibility Build (estimated Q2)</s></li><li>Vue 2.7 (estimated Q2/3)</li><li>SSR support in Vite</li><li>Vuex 5 (TBD)</li></ul><p>In addition, we are excited about other frameworks and libraries in the ecosystem making progress towards Vue 3 support. And of course, we can’t wait to see what new ideas and tools you all have as we embark on a new chapter in the Vue.js roadmap.</p></div></div></div><footer class="text-sm font-medium leading-5 divide-y divide-gray-200 dark:divide-slate-200/5 xl:col-start-1 xl:row-start-2"><div class="py-8"><h2 class="text-xs tracking-wide uppercase text-gray-500 dark:text-white"> Next Article </h2><div class="link"><a href="/blog/posts/2022-year-in-review.html">2022 Year In Review</a></div></div><!----><div class="pt-8"><a class="link" href="/blog/">← Back to the blog</a></div></footer></div></article></main></div></div>
    <script>window.__VP_HASH_MAP__=JSON.parse("{\"index.md\":\"DzYrHPtc\",\"posts_2022-year-in-review.md\":\"CClB9OOf\",\"posts_hello-2021.md\":\"CWsHxIdM\",\"posts_how-to-use-images.md\":\"DriCg1x6\"}");window.__VP_SITE_DATA__=JSON.parse("{\"lang\":\"en-US\",\"dir\":\"ltr\",\"title\":\"The Vue Point\",\"description\":\"The official blog for the Vue.js project\",\"base\":\"/blog/\",\"head\":[],\"router\":{\"prefetchLinks\":true},\"appearance\":true,\"themeConfig\":{},\"locales\":{},\"scrollOffset\":134,\"cleanUrls\":false}");</script>
    
  </body>
</html>