import{_ as s,c as a,o as t,k as e}from"./chunks/framework._wHCgU2P.js";const l="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20fill='none'%20version='1.1'%20width='512'%20height='512'%20viewBox='0%200%20512%20512'%3e%3cdefs%3e%3cclipPath%20id='master_svg0_50_3'%3e%3crect%20x='0'%20y='0'%20width='512'%20height='512'%20rx='75'/%3e%3c/clipPath%3e%3c/defs%3e%3cg%20clip-path='url(%23master_svg0_50_3)'%3e%3crect%20x='0'%20y='0'%20width='512'%20height='512'%20rx='75'%20fill='%23000000'%20fill-opacity='1'/%3e%3cg%3e%3cpath%20d='M223.68,391L289.03999999999996,391L289.03999999999996,305.88L363.52,144L295.12,144L276.88,195.68C270.42,214.3,263.96,231.4,257.5,250.78L255.98000000000002,250.78C249.51999999999998,231.4,243.44,214.3,237.36,195.68L219.12,144L149.2,144L223.68,305.88L223.68,391Z'%20fill='%23D9D9D9'%20fill-opacity='1'/%3e%3c/g%3e%3c/g%3e%3c/svg%3e",g=JSON.parse('{"title":"如何在博客中使用图片","description":"演示如何在 VitePress 博客文章中使用图片资源","frontmatter":{"title":"如何在博客中使用图片","description":"演示如何在 VitePress 博客文章中使用图片资源","date":"2025-08-07T00:00:00.000Z","author":{"name":"Blog Author","link":"https://github.com/author"}},"headers":[],"relativePath":"posts/how-to-use-images.md","filePath":"posts/how-to-use-images.md"}'),h={name:"posts/how-to-use-images.md"};function n(o,i,d,r,p,k){return t(),a("div",null,i[0]||(i[0]=[e('<h1 id="如何在博客中使用图片" tabindex="-1">如何在博客中使用图片 <a class="header-anchor" href="#如何在博客中使用图片" aria-label="Permalink to &quot;如何在博客中使用图片&quot;">​</a></h1><p>这篇文章演示了如何在 VitePress 博客中使用图片资源。</p><h2 id="使用-public-目录中的图片" tabindex="-1">使用 public 目录中的图片 <a class="header-anchor" href="#使用-public-目录中的图片" aria-label="Permalink to &quot;使用 public 目录中的图片&quot;">​</a></h2><p>VitePress 会自动处理 <code>blog/public</code> 目录下的静态资源。你可以直接引用这些文件：</p><h3 id="示例-引用-logo" tabindex="-1">示例：引用 logo <a class="header-anchor" href="#示例-引用-logo" aria-label="Permalink to &quot;示例：引用 logo&quot;">​</a></h3><div class="language-markdown vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">markdown</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">![</span><span style="--shiki-light:#032F62;--shiki-light-text-decoration:underline;--shiki-dark:#DBEDFF;--shiki-dark-text-decoration:underline;">Logo</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">](</span><span style="--shiki-light:#24292E;--shiki-light-text-decoration:underline;--shiki-dark:#E1E4E8;--shiki-dark-text-decoration:underline;">../public/images/pic.svg</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">)</span></span></code></pre></div><p>实际效果：</p><p><img src="'+l+'" alt="Logo"></p><h2 id="图片路径规则" tabindex="-1">图片路径规则 <a class="header-anchor" href="#图片路径规则" aria-label="Permalink to &quot;图片路径规则&quot;">​</a></h2><ol><li><p><strong>绝对路径</strong>：以 <code>/blog/</code> 开头，指向 <code>blog/public</code> 目录</p><div class="language-markdown vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">markdown</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">![</span><span style="--shiki-light:#032F62;--shiki-light-text-decoration:underline;--shiki-dark:#DBEDFF;--shiki-dark-text-decoration:underline;">图片描述</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">](</span><span style="--shiki-light:#24292E;--shiki-light-text-decoration:underline;--shiki-dark:#E1E4E8;--shiki-dark-text-decoration:underline;">/your-image.png</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">)</span></span></code></pre></div></li><li><p><strong>相对路径</strong>：相对于当前 markdown 文件</p><div class="language-markdown vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">markdown</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">![</span><span style="--shiki-light:#032F62;--shiki-light-text-decoration:underline;--shiki-dark:#DBEDFF;--shiki-dark-text-decoration:underline;">图片描述</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">](</span><span style="--shiki-light:#24292E;--shiki-light-text-decoration:underline;--shiki-dark:#E1E4E8;--shiki-dark-text-decoration:underline;">./images/your-image.png</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">)</span></span></code></pre></div></li></ol><h2 id="添加新图片的步骤" tabindex="-1">添加新图片的步骤 <a class="header-anchor" href="#添加新图片的步骤" aria-label="Permalink to &quot;添加新图片的步骤&quot;">​</a></h2><ol><li>将图片文件放入 <code>blog/public</code> 目录</li><li>在 markdown 文件中使用 <code>/blog/filename.ext</code> 路径引用</li><li>运行 <code>pnpm blog:build</code> 重新构建</li></ol><h2 id="图片优化建议" tabindex="-1">图片优化建议 <a class="header-anchor" href="#图片优化建议" aria-label="Permalink to &quot;图片优化建议&quot;">​</a></h2><ul><li>使用适当的图片格式（PNG、JPG、SVG、WebP）</li><li>压缩图片以减少文件大小</li><li>为图片添加有意义的 alt 文本</li><li>考虑使用响应式图片</li></ul><h2 id="html-标签方式" tabindex="-1">HTML 标签方式 <a class="header-anchor" href="#html-标签方式" aria-label="Permalink to &quot;HTML 标签方式&quot;">​</a></h2><p>你也可以使用 HTML <code>&lt;img&gt;</code> 标签获得更多控制：</p><div class="language-html vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">html</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">img</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> src</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;./images/pic.svg&quot;</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> alt</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;Logo&quot;</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> width</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;100&quot;</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> height</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;100&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> /&gt;</span></span></code></pre></div><p>这样你就可以在博客文章中自由使用图片了！</p>',18)]))}const E=s(h,[["render",n]]);export{g as __pageData,E as default};
