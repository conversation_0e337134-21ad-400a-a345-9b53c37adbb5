<script setup lang="ts">
const colorMode = useColorMode()

// 计算当前是否为深色模式
const isDark = computed({
  get: () => colorMode.value === 'dark',
  set: (value: boolean) => {
    colorMode.preference = value ? 'dark' : 'light'
  }
})
</script>

<template>
  <Tooltip>
    <TooltipTrigger as-child>
      <div class="flex items-center gap-2">
        <Icon
          name="lucide:sun"
          class="h-4 w-4 text-muted-foreground transition-colors"
          :class="{ 'text-foreground': !isDark }"
        />
        <Switch
          v-model:checked="isDark"
          aria-label="Toggle dark mode"
        />
        <Icon
          name="lucide:moon"
          class="h-4 w-4 text-muted-foreground transition-colors"
          :class="{ 'text-foreground': isDark }"
        />
      </div>
    </TooltipTrigger>
    <TooltipContent>
      <p>{{ isDark ? '切换到浅色模式' : '切换到深色模式' }}</p>
    </TooltipContent>
  </Tooltip>
</template>
