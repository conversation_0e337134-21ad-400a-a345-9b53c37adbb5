<script setup lang="ts">
const colorMode = useColorMode()

// 使用 ref 来管理状态，避免 SSR 水合问题
const isDark = ref(false)

// 切换模式的处理函数
const toggleTheme = (value: boolean) => {
  colorMode.preference = value ? 'dark' : 'light'
}

// 客户端挂载后同步状态
onMounted(() => {
  isDark.value = colorMode.value === 'dark'
})

// 监听 colorMode 变化
watch(() => colorMode.value, (newValue) => {
  isDark.value = newValue === 'dark'
})
</script>

<template>
  <Switch
    :model-value="isDark"
    @update:model-value="toggleTheme"
    aria-label="Toggle dark mode"
  >
    <template #thumb>
      <div class="relative flex h-5 w-5 items-center justify-center">
        <Icon
          name="lucide:moon"
          class="absolute size-3"
          v-show="isDark"
        />
        <Icon
          name="lucide:sun"
          class="absolute size-3"
          v-show="!isDark"
        />
      </div>
    </template>
  </Switch>
</template>
