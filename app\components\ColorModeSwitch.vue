<script setup lang="ts">
const colorMode = useColorMode()

// 使用 ref 来管理状态，避免 SSR 水合问题
const isDark = ref(false)

// 切换模式的处理函数
const toggleTheme = (value: boolean) => {
  colorMode.preference = value ? 'dark' : 'light'
}

// 客户端挂载后同步状态
onMounted(() => {
  isDark.value = colorMode.value === 'dark'
})

// 监听 colorMode 变化
watch(() => colorMode.value, (newValue) => {
  isDark.value = newValue === 'dark'
})
</script>

<template>
  <ClientOnly>
    <Switch
      :model-value="isDark"
      @update:model-value="toggleTheme"
      aria-label="Toggle dark mode"
    >
      <template #thumb>
        <Icon
          :name="isDark ? 'lucide:moon' : 'lucide:sun'"
          class="size-3 transition-all duration-200"
        />
      </template>
    </Switch>
    <template #fallback>
      <!-- 服务端渲染时的占位符 -->
      <div class="inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-input bg-input shadow-xs">
        <div class="bg-background block size-4 rounded-full transition-transform translate-x-0">
          <Icon name="lucide:sun" class="size-3 p-0.5" />
        </div>
      </div>
    </template>
  </ClientOnly>
</template>
