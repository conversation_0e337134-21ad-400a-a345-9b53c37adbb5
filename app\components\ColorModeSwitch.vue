<script setup lang="ts">
const colorMode = useColorMode()

// 切换明暗模式
const toggleColorMode = () => {
  colorMode.preference = colorMode.value === 'dark' ? 'light' : 'dark'
}

// 计算当前是否为深色模式
const isDark = computed(() => colorMode.value === 'dark')
</script>

<template>
  <Tooltip>
    <TooltipTrigger as-child>
      <Button
        variant="ghost"
        size="icon"
        @click="toggleColorMode"
        aria-label="Toggle color mode"
      >
        <Icon
          v-if="isDark"
          name="lucide:sun"
          class="h-4 w-4"
        />
        <Icon
          v-else
          name="lucide:moon"
          class="h-4 w-4"
        />
      </Button>
    </TooltipTrigger>
    <TooltipContent>
      <p>{{ isDark ? '切换到浅色模式' : '切换到深色模式' }}</p>
    </TooltipContent>
  </Tooltip>
</template>
