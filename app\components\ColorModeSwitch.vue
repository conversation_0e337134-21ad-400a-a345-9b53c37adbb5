<script setup lang="ts">
const colorMode = useColorMode()

// 使用 ref 来避免水合错误
const isDark = ref(false)
const isMounted = ref(false)

// 切换模式的处理函数
const toggleTheme = (checked: boolean) => {
  colorMode.preference = checked ? 'dark' : 'light'
  isDark.value = checked
}

// 在客户端挂载后同步状态
onMounted(() => {
  isMounted.value = true
  isDark.value = colorMode.value === 'dark'

  // 监听 colorMode 变化
  watch(() => colorMode.value, (newValue) => {
    isDark.value = newValue === 'dark'
  })
})
</script>

<template>
  <Switch
    v-if="isMounted"
    :checked="isDark"
    @update:checked="toggleTheme"
    aria-label="Toggle dark mode"
  >
    <template #thumb>
      <Icon
        v-if="isDark"
        name="lucide:moon"
        class="size-3"
      />
      <Icon
        v-else
        name="lucide:sun"
        class="size-3"
      />
    </template>
  </Switch>
  <!-- 服务端渲染占位符 -->
  <div
    v-else
    class="inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent bg-input transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50"
  >
    <div class="pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform translate-x-0">
      <div class="flex h-5 w-5 items-center justify-center">
        <Icon name="lucide:sun" class="size-3" />
      </div>
    </div>
  </div>
</template>
