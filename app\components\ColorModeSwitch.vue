<script setup lang="ts">
const colorMode = useColorMode()

// 计算当前是否为深色模式
const isDark = computed(() => colorMode.value === 'dark')

// 切换模式的处理函数
const toggleTheme = (value: boolean) => {
  colorMode.preference = value ? 'dark' : 'light'
}
</script>

<template>
  <Switch
    :model-value="isDark"
    @update:model-value="toggleTheme"
    aria-label="Toggle dark mode"
  >
    <template #thumb>
      <Icon
        v-if="isDark"
        name="lucide:moon"
        class="size-3"
      />
      <Icon
        v-else
        name="lucide:sun"
        class="size-3"
      />
    </template>
  </Switch>
</template>
