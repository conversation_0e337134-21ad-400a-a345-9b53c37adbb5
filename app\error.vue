<script setup lang="ts">
import type { NuxtError } from '#app'



useSeoMeta({
  title: 'Page not found',
  description: 'We are sorry but this page could not be found.',
})

defineProps({
  error: {
    type: Object as PropType<NuxtError>,
    required: true,
  },
  message: {
    type: String,
    default: 'This is not the page you\'re looking for.',
  },
})

useHead({
  htmlAttrs: {
    lang: 'en',
  },
})
</script>

<template>
  <div class="flex min-h-screen flex-col items-center justify-center">
    <h1 class="text-3xl font-bold tracking-tight text-primary md:text-5xl">
      {{ error?.statusCode || 404 }}
    </h1>
    <p class="mt-6 text-center text-muted-foreground">
      {{ error?.message && error.message !== (error.name || error.statusMessage || '') ? error.message : message }}
    </p>
    <div class="mt-10 flex items-center justify-center gap-x-6">
      <Button
        as-child
        class="mt-4"
        size="lg"
      >
        <NuxtLink href="/">
          Go to homepage
        </NuxtLink>
      </Button>
    </div>
  </div>
</template>
