@import "tailwindcss";

@plugin "@tailwindcss/typography";

.vp-code span {
  color: var(--shiki-dark, inherit);
}

:root {
  --c-brand: #3eaf7c;
  --c-brand-light: #4abf8a;
}

nav img {
  vertical-align: middle;
}

p img {
  margin: 0px auto;
}

.prose hr {
  border-top: 1px solid #e5e7eb;
}

.link {
  color: var(--c-brand);
}

.link:hover {
  color: var(--c-brand-light);
}

.header-anchor {
  display: none;
}

h3 .header-anchor {
  display: inline-block;
  position: absolute;
  left: -1em;
  text-decoration: none;
  color: var(--c-brand);
}

h3 .header-anchor:before {
  content: '#';
}

/**
 * prism.js tomorrow night eighties for JavaScript, CoffeeScript, CSS and HTML.
 * Based on https://github.com/chriskempson/tomorrow-theme
 *
 * <AUTHOR>
 */
.token.comment,
.token.block-comment,
.token.prolog,
.token.doctype,
.token.cdata {
  color: #999;
}

.token.punctuation {
  color: #ccc;
}

.token.tag,
.token.attr-name,
.token.namespace,
.token.deleted {
  color: #e2777a;
}

.token.function-name {
  color: #6196cc;
}

.token.boolean,
.token.number,
.token.function {
  color: #f08d49;
}

.token.property,
.token.class-name,
.token.constant,
.token.symbol {
  color: #f8c555;
}

.token.selector,
.token.important,
.token.atrule,
.token.keyword,
.token.builtin {
  color: #cc99cd;
}

.token.string,
.token.char,
.token.attr-value,
.token.regex,
.token.variable {
  color: #7ec699;
}

.token.operator,
.token.entity,
.token.url {
  color: #67cdcc;
}

.token.important,
.token.bold {
  font-weight: bold;
}

.token.italic {
  font-style: italic;
}

.token.entity {
  cursor: help;
}

.token.inserted {
  color: #67cdcc;
}

button.copy {
  display: none;
}

span.lang {
  position: absolute;
  right: 0.5em;
  font-size: 0.75em;
  color: #999;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
    'Liberation Mono', 'Courier New', monospace;
}

.custom-block {
  margin: 28px 0;
  padding: 0 24px 2px;
  border-radius: 8px;
  overflow-x: auto;
  position: relative;
  font-size: 14px;
  line-height: 1.3;
  font-weight: 500;
  color: #444;
  background-color: #f9f9f9;
}
.custom-block .custom-block-title {
  margin-bottom: 8px;
  font-size: 15px;
  font-weight: bold;
}

.custom-block.tip {
  border: 1px solid #42b883;
}
.custom-block.tip:before {
  color: #42b883;
}

.prose
  :where(:not(pre) > code):not(
    :where([class~='not-prose'], [class~='not-prose'] *)
  ) {
  color: #0a3760;
  padding: 0.25em 0.4em;
  border-radius: 4px;
  background-color: #eee;
}

.prose
  :where(code):not(
    :where([class~='not-prose'], [class~='not-prose'] *)
  )::before {
  content: '' !important;
}

.prose
  :where(code):not(
    :where([class~='not-prose'], [class~='not-prose'] *)
  )::after {
  content: '' !important;
}

@media (prefers-color-scheme: dark) {
  .prose
    :where(:not(pre) > code):not(
      :where([class~='not-prose'], [class~='not-prose'] *)
    ) {
    color: #d1e9ff;
    background-color: #3c3a5b;
  }
}

@media (max-width: 518px) {
  .prose img {
    max-width: 100% !important;
  }
}
